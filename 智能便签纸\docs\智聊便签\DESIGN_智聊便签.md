# 智聊便签系统架构设计

## 整体架构图

```mermaid
graph TB
    subgraph "Electron应用"
        subgraph "主进程 (Main Process)"
            A[应用入口 main.js]
            B[窗口管理器 WindowManager]
            C[数据管理器 DataManager]
            D[IPC处理器 IPCHandler]
        end
        
        subgraph "分析面板 (Renderer Process)"
            E[分析面板组件 AnalysisPanel]
            F[文本分析器 TextAnalyzer]
            G[事件卡片组件 EventCard]
            H[UI状态管理 AnalysisStore]
        end
        
        subgraph "便签窗口 (Multiple Renderer Processes)"
            I[便签组件 StickyNote]
            J[控制面板 ControlPanel]
            K[便签状态 NoteStore]
        end
    end
    
    subgraph "外部依赖"
        L[chrono-node]
        M[electron-store]
        N[系统剪贴板]
    end
    
    A --> B
    A --> C
    A --> D
    B --> E
    B --> I
    C --> M
    F --> L
    E --> F
    E --> G
    I --> J
    J --> N
    D -.-> H
    D -.-> K
```

## 分层设计

### 1. 主进程层 (Main Process Layer)
**职责**: 应用生命周期管理、窗口创建、数据持久化、进程间通信

**核心组件**:
- **应用入口 (main.js)**: Electron应用启动和配置
- **窗口管理器 (WindowManager)**: 管理分析面板和便签窗口的创建、销毁
- **数据管理器 (DataManager)**: 统一管理便签数据的读写和同步
- **IPC处理器 (IPCHandler)**: 处理渲染进程的通信请求

### 2. 渲染进程层 (Renderer Process Layer)
**职责**: 用户界面渲染、用户交互处理、业务逻辑执行

#### 2.1 分析面板进程
- **分析面板组件 (AnalysisPanel)**: 主界面组件
- **文本分析器 (TextAnalyzer)**: 集成chrono-node进行文本解析
- **事件卡片组件 (EventCard)**: 显示和编辑解析结果
- **UI状态管理 (AnalysisStore)**: 管理分析面板的状态

#### 2.2 便签窗口进程
- **便签组件 (StickyNote)**: 便签显示和基础交互
- **控制面板 (ControlPanel)**: 悬浮控件的实现
- **便签状态 (NoteStore)**: 管理单个便签的状态

### 3. 数据层 (Data Layer)
**职责**: 数据持久化、数据模型定义

**核心组件**:
- **electron-store**: 本地JSON数据存储
- **数据模型**: 便签和设置的数据结构定义

## 核心组件详细设计

### 主进程组件

#### WindowManager (窗口管理器)
```typescript
class WindowManager {
  private analysisWindow: BrowserWindow | null = null;
  private noteWindows: Map<string, BrowserWindow> = new Map();
  
  // 创建分析面板
  createAnalysisWindow(): void
  
  // 创建便签窗口
  createNoteWindow(noteData: NoteData): BrowserWindow
  
  // 关闭便签窗口
  closeNoteWindow(noteId: string): void
  
  // 获取所有窗口状态
  getAllWindowStates(): WindowState[]
}
```

#### DataManager (数据管理器)
```typescript
class DataManager {
  private store: Store<AppData>;
  
  // 保存便签数据
  saveNote(note: NoteData): void
  
  // 获取所有便签
  getAllNotes(): NoteData[]
  
  // 更新便签
  updateNote(noteId: string, updates: Partial<NoteData>): void
  
  // 删除便签
  deleteNote(noteId: string): void
  
  // 获取设置
  getSettings(): AppSettings
  
  // 更新设置
  updateSettings(settings: Partial<AppSettings>): void
}
```

### 渲染进程组件

#### TextAnalyzer (文本分析器)
```typescript
class TextAnalyzer {
  // 分析文本并提取事件
  analyzeText(text: string): Promise<ExtractedEvent[]>
  
  // 解析时间表达式
  parseTimeExpression(text: string): Date | null
  
  // 提取事件描述
  extractEventDescription(text: string, timeInfo: any): string
  
  // 验证解析结果
  validateResult(event: ExtractedEvent): boolean
}
```

#### StickyNote (便签组件)
```typescript
interface StickyNoteProps {
  noteData: NoteData;
  onUpdate: (updates: Partial<NoteData>) => void;
  onDelete: () => void;
  onComplete: () => void;
}

const StickyNote: React.FC<StickyNoteProps> = ({ ... }) => {
  // 便签渲染逻辑
}
```

## 模块依赖关系图

```mermaid
graph LR
    subgraph "主进程模块"
        A[main.js] --> B[WindowManager]
        A --> C[DataManager]
        A --> D[IPCHandler]
        C --> E[electron-store]
    end
    
    subgraph "分析面板模块"
        F[AnalysisPanel] --> G[TextAnalyzer]
        F --> H[EventCard]
        F --> I[AnalysisStore]
        G --> J[chrono-node]
    end
    
    subgraph "便签窗口模块"
        K[StickyNote] --> L[ControlPanel]
        K --> M[NoteStore]
        L --> N[clipboard API]
    end
    
    B -.-> F
    B -.-> K
    D -.-> I
    D -.-> M
```

## 接口契约定义

### IPC通信接口
```typescript
// 主进程 -> 渲染进程
interface MainToRenderer {
  'note-data-updated': (noteData: NoteData) => void;
  'settings-updated': (settings: AppSettings) => void;
}

// 渲染进程 -> 主进程
interface RendererToMain {
  'create-note': (noteData: Omit<NoteData, 'id'>) => Promise<string>;
  'update-note': (noteId: string, updates: Partial<NoteData>) => Promise<void>;
  'delete-note': (noteId: string) => Promise<void>;
  'get-all-notes': () => Promise<NoteData[]>;
  'analyze-text': (text: string) => Promise<ExtractedEvent[]>;
  'copy-to-clipboard': (text: string) => Promise<void>;
}
```

### 数据接口
```typescript
interface NoteData {
  id: string;
  content: string;
  eventTimestamp: string; // ISO timestamp
  position: { x: number; y: number };
  size: { width: number; height: number };
  color: 'yellow' | 'blue' | 'green' | 'pink' | 'orange';
  isPinned: boolean;
  isCompleted: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ExtractedEvent {
  title: string;
  description: string;
  timestamp: Date | null;
  confidence: number; // 0-1, 解析置信度
  originalText: string;
}

interface AppSettings {
  launchAtLogin: boolean;
  defaultNoteColor: string;
  analysisWindowPosition?: { x: number; y: number };
}
```

## 数据流向图

```mermaid
sequenceDiagram
    participant U as 用户
    participant AP as 分析面板
    participant TA as 文本分析器
    participant MP as 主进程
    participant DM as 数据管理器
    participant SW as 便签窗口
    
    U->>AP: 粘贴聊天记录
    U->>AP: 点击"智能分析"
    AP->>TA: 分析文本
    TA->>TA: chrono-node解析
    TA->>AP: 返回事件列表
    AP->>U: 显示事件卡片
    
    U->>AP: 编辑事件信息
    U->>AP: 点击"创建便签"
    AP->>MP: IPC: create-note
    MP->>DM: 保存便签数据
    MP->>SW: 创建便签窗口
    SW->>U: 显示桌面便签
    
    U->>SW: 便签交互操作
    SW->>MP: IPC: update-note
    MP->>DM: 更新便签数据
```

## 异常处理策略

### 1. 文本分析异常
- **chrono-node解析失败**: 返回空结果，提示用户手动输入
- **无效时间表达**: 提供默认时间选择器
- **文本过长**: 截断处理，提示用户分段分析

### 2. 窗口管理异常
- **窗口创建失败**: 记录错误日志，提示用户重试
- **窗口位置超出屏幕**: 自动调整到可见区域
- **多窗口冲突**: 实现窗口层级管理

### 3. 数据持久化异常
- **存储空间不足**: 提示用户清理旧数据
- **数据损坏**: 备份机制，尝试恢复或重置
- **读写权限问题**: 提示用户检查权限设置

### 4. IPC通信异常
- **通信超时**: 实现重试机制
- **消息格式错误**: 数据验证和错误提示
- **进程崩溃**: 自动重启机制

## 性能优化策略

### 1. 内存管理
- 便签窗口懒加载
- 不活跃窗口资源回收
- 定期清理无用数据

### 2. 渲染优化
- React组件优化（memo, useMemo）
- 虚拟化长列表
- 图片和资源懒加载

### 3. 存储优化
- 增量数据更新
- 数据压缩存储
- 定期数据清理

### 4. 启动优化
- 延迟加载非核心模块
- 预编译模板
- 缓存常用数据

## 安全考虑

### 1. 数据安全
- 本地数据加密存储
- 敏感信息脱敏处理
- 定期数据备份

### 2. 系统安全
- 限制文件系统访问权限
- 禁用不安全的Node.js API
- 内容安全策略(CSP)配置

### 3. 用户隐私
- 不收集用户数据
- 本地处理所有信息
- 透明的数据使用说明