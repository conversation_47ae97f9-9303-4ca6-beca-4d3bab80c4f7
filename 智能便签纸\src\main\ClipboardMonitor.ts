
import { clipboard } from 'electron';
import { TextAnalyzer } from './TextAnalyzer';
import { SuggestionManager } from './SuggestionManager';
import { logger } from './Logger';

export class ClipboardMonitor {
    private textAnalyzer: TextAnalyzer;
    private suggestionManager: SuggestionManager;
    private lastClipboardText: string = '';
    private monitorInterval: NodeJS.Timeout | null = null;

    constructor(textAnalyzer: TextAnalyzer, suggestionManager: SuggestionManager) {
        this.textAnalyzer = textAnalyzer;
        this.suggestionManager = suggestionManager;
    }

    public startMonitoring(interval: number = 1000): void {
        if (this.monitorInterval) {
            this.stopMonitoring();
        }
        logger.log('Clipboard monitoring started.');
        this.monitorInterval = setInterval(() => {
            this.checkClipboard();
        }, interval);
    }

    public stopMonitoring(): void {
        if (this.monitorInterval) {
            clearInterval(this.monitorInterval);
            this.monitorInterval = null;
            logger.log('Clipboard monitoring stopped.');
        }
    }

    private async checkClipboard(): Promise<void> {
        try {
            const currentText = clipboard.readText();
            if (currentText && currentText !== this.lastClipboardText) {
                this.lastClipboardText = currentText;
                logger.log('Clipboard changed. New text:', currentText);

                const events = await this.textAnalyzer.analyzeText(currentText);
                logger.log('Text analysis result:', events);

                if (events && events.length > 0) {
                    const highConfidenceEvent = events.sort((a, b) => b.confidence - a.confidence)[0];
                    logger.log('High confidence event found. Calling suggestion manager...', highConfidenceEvent);
                    this.suggestionManager.showSuggestion(highConfidenceEvent);
                }
            }
        } catch (error) {
            logger.error('Error checking clipboard:', error);
        }
    }
}
