# 剪贴板监控使用说明

## 功能概述

由于直接监控微信聊天窗口存在技术限制，我们提供了剪贴板监控作为替代解决方案。该功能可以：

1. **自动监控剪贴板内容变化**
2. **智能提取时间相关信息**
3. **自动创建便签提醒**
4. **支持多种时间格式识别**

## 使用步骤

### 1. 启动剪贴板监控

1. 打开智聊便签主界面
2. 点击"剪贴板监控"按钮
3. 在监控界面点击"启动监控"
4. 监控状态指示器变为绿色表示启动成功

### 2. 复制包含时间信息的文本

从微信或其他应用复制包含时间信息的文本，例如：
- "明天下午3点开会"
- "下周五晚上8点聚餐"
- "2024年1月15日上午10点面试"
- "今晚7点半看电影"

### 3. 自动创建便签

系统会自动：
1. 检测剪贴板内容变化
2. 分析文本中的时间信息
3. 提取事件描述
4. 创建桌面便签提醒

## 支持的时间格式

### 相对时间
- 今天、明天、后天
- 下周一、下周二...
- 本月、下月
- 今年、明年

### 绝对时间
- 2024年1月15日
- 1月15日
- 15号
- 周一、周二...

### 时间点
- 上午9点
- 下午3点半
- 晚上8:30
- 19:00

### 组合格式
- "明天下午2点开会"
- "下周三上午10:30面试"
- "1月20日晚上7点聚餐"

## 监控界面功能

### 状态显示
- **绿色指示器**：监控运行中
- **红色指示器**：监控已停止

### 内容显示
- **当前剪贴板内容**：显示最新的剪贴板文本
- **提取的事件**：显示识别出的时间事件
- **监控日志**：显示操作记录和状态信息

### 操作按钮
- **启动/停止监控**：控制监控状态
- **分析当前剪贴板**：手动分析剪贴板内容
- **清空日志**：清除监控日志
- **关闭**：关闭监控窗口

## 使用技巧

### 1. 提高识别准确性
- 复制完整的句子而不是片段
- 包含明确的时间和事件描述
- 使用常见的时间表达方式

### 2. 批量处理
- 可以连续复制多条包含时间的文本
- 系统会为每条识别出的事件创建独立便签

### 3. 手动分析
- 如果自动监控未识别，可使用"分析当前剪贴板"功能
- 适用于复杂或特殊格式的时间表达

## 注意事项

1. **隐私保护**：剪贴板内容仅在本地处理，不会上传到服务器
2. **性能影响**：监控会定期检查剪贴板，对系统性能影响很小
3. **兼容性**：支持所有Windows应用程序的剪贴板操作
4. **准确性**：时间识别基于自然语言处理，复杂表达可能需要手动调整

## 常见问题

### Q: 为什么有些时间没有被识别？
A: 可能是时间表达过于复杂或不常见，建议使用更标准的时间格式。

### Q: 可以同时监控多个应用吗？
A: 是的，剪贴板监控对所有应用程序都有效。

### Q: 监控会影响其他剪贴板工具吗？
A: 不会，我们只读取剪贴板内容，不会干扰其他工具。

### Q: 如何停止自动创建便签？
A: 在监控界面点击"停止监控"即可。

## 未来改进

1. **更智能的时间识别**：支持更多时间表达格式
2. **事件分类**：自动识别会议、约会、提醒等不同类型
3. **批量管理**：提供批量编辑和删除功能
4. **导入导出**：支持从其他应用导入日程安排

---

通过剪贴板监控，您可以轻松地将微信聊天中的重要时间信息转换为桌面便签提醒，确保不错过任何重要事件。