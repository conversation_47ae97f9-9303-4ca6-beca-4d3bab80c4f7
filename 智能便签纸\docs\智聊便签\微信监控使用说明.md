# 智聊便签 - 微信监控功能使用说明

## 🎯 核心功能

智聊便签现在可以**自动监控微信聊天内容**，无需手动复制粘贴！

### ✨ 主要特性

1. **剪贴板监控** - 当您复制微信聊天内容时，自动分析时间信息
2. **智能识别** - 自动识别包含时间的消息（如："明天下午3点开会"）
3. **自动创建便签** - 发现时间信息后自动创建桌面便签提醒
4. **系统通知** - 创建便签时显示系统通知

## 🚀 使用方法

### 1. 启动微信监控
- 打开智聊便签应用
- 在分析窗口中点击 **"开始监控"** 按钮
- 状态显示为 **"监控中..."** 表示已启动

### 2. 正常使用微信
- 正常查看微信聊天
- 当看到包含时间信息的消息时，**复制该消息**
- 系统会自动分析并创建便签（如果包含时间信息）

### 3. 自动便签创建
当复制的内容包含以下时间信息时，会自动创建便签：
- **具体时间**: "3点"、"下午2:30"、"晚上8点"
- **日期表达**: "明天"、"后天"、"周一"、"3月15日"
- **会议词汇**: "开会"、"聚会"、"约会"、"见面"
- **提醒词汇**: "提醒"、"记住"、"别忘"

## 📝 使用示例

### 示例1：会议提醒
**微信消息**: "明天下午3点在会议室开会讨论项目进度"
**操作**: 复制这条消息
**结果**: 自动创建便签 "📱 微信提醒 - 明天下午3点开会讨论项目进度"

### 示例2：约会提醒
**微信消息**: "周六晚上7点一起吃饭"
**操作**: 复制这条消息
**结果**: 自动创建便签 "📱 微信提醒 - 周六晚上7点一起吃饭"

## ⚙️ 监控设置

### 启动监控
```
状态: 监控中...
方法: 剪贴板监控 + 窗口标题监控
```

### 停止监控
- 点击 **"停止监控"** 按钮
- 或关闭应用程序

## 🔧 技术原理

1. **剪贴板监控**: 每2秒检查剪贴板内容变化
2. **智能过滤**: 只分析包含时间关键词的文本
3. **时间解析**: 使用 chrono-node 解析自然语言时间表达
4. **便签创建**: 自动生成带有时间信息的桌面便签

## 💡 使用技巧

### 最佳实践
1. **保持监控开启**: 启动应用后立即开启监控
2. **复制完整消息**: 复制包含完整时间信息的消息
3. **及时查看便签**: 创建的便签会显示在桌面上

### 注意事项
- 只有复制到剪贴板的内容才会被分析
- 需要包含明确的时间信息才会创建便签
- 便签会自动置顶显示，方便查看

## 🎨 便签样式

自动创建的便签具有以下特点：
- **蓝色主题** - 区别于手动创建的便签
- **自动置顶** - 确保重要提醒不被遗漏
- **完整信息** - 包含原始消息和解析的时间
- **可编辑** - 可以手动修改便签内容

## 🔍 故障排除

### 监控不工作
1. 确认监控状态为 "监控中..."
2. 检查是否有复制操作
3. 确认消息包含时间关键词

### 没有创建便签
1. 检查消息是否包含明确的时间表达
2. 确认复制的文本长度适中（5-1000字符）
3. 查看控制台日志了解详细信息

### 便签位置重叠
- 系统会自动随机放置便签位置
- 可以手动拖拽调整便签位置

---

**🎉 现在您可以真正实现无感知的微信聊天监控和自动提醒创建！**