/**
 * IPC通信处理器 - 处理主进程和渲染进程之间的通信
 */

import { ipcMain, clipboard } from 'electron';
import { WindowManager } from './WindowManager';
import { DataManager } from './DataManager';
import { TextAnalyzer } from './TextAnalyzer';
import { NoteData, ExtractedEvent } from '../types/index';

export class IPCHandler {
  private windowManager: WindowManager;
  private dataManager: DataManager;
  private textAnalyzer: TextAnalyzer;

  constructor(windowManager: WindowManager, dataManager: DataManager) {
    this.windowManager = windowManager;
    this.dataManager = dataManager;
    this.textAnalyzer = new TextAnalyzer();
  }

  

  /**
   * 设置所有IPC处理器
   */
  setupHandlers(): void {
    // 便签相关操作
    this.setupNoteHandlers();
    
    // 设置相关操作
    this.setupSettingsHandlers();
    
    // 文本分析相关操作
    this.setupAnalysisHandlers();
    
    // 窗口相关操作
    this.setupWindowHandlers();
    
    // 系统相关操作
    this.setupSystemHandlers();

    console.log('IPC处理器设置完成');
  }

  /**
   * 设置便签相关的IPC处理器
   */
  private setupNoteHandlers(): void {
    // 创建便签
    ipcMain.handle('create-note', async (event, noteData: Omit<NoteData, 'id' | 'createdAt' | 'updatedAt'>) => {
      try {
        const noteId = await this.dataManager.createNote(noteData);
        
        // 创建便签窗口
        const fullNoteData = await this.dataManager.getNote(noteId);
        if (fullNoteData) {
          await this.windowManager.createNoteWindow(fullNoteData);
        }
        
        return noteId;
      } catch (error) {
        console.error('创建便签失败:', error);
        throw error;
      }
    });

    // 更新便签
    ipcMain.handle('update-note', async (event, noteId: string, updates: Partial<NoteData>) => {
      try {
        await this.dataManager.updateNote(noteId, updates);
        
        // 通知便签窗口更新
        const noteWindow = this.windowManager.getNoteWindow(noteId);
        if (noteWindow && !noteWindow.isDestroyed()) {
          const updatedNote = await this.dataManager.getNote(noteId);
          if (updatedNote) {
            noteWindow.webContents.send('note-data-updated', updatedNote);
          }
        }
      } catch (error) {
        console.error('更新便签失败:', error);
        throw error;
      }
    });

    // 删除便签
    ipcMain.handle('delete-note', async (event, noteId: string) => {
      try {
        await this.dataManager.deleteNote(noteId);
        this.windowManager.closeNoteWindow(noteId);
      } catch (error) {
        console.error('删除便签失败:', error);
        throw error;
      }
    });

    // 完成便签
    ipcMain.handle('complete-note', async (event, noteId: string) => {
      try {
        await this.dataManager.completeNote(noteId);
        this.windowManager.closeNoteWindow(noteId);
      } catch (error) {
        console.error('完成便签失败:', error);
        throw error;
      }
    });

    // 获取所有便签
    ipcMain.handle('get-all-notes', async () => {
      try {
        return await this.dataManager.getAllNotes();
      } catch (error) {
        console.error('获取便签列表失败:', error);
        throw error;
      }
    });
  }

  /**
   * 设置设置相关的IPC处理器
   */
  private setupSettingsHandlers(): void {
    // 获取设置
    ipcMain.handle('get-settings', async () => {
      try {
        return await this.dataManager.getSettings();
      } catch (error) {
        console.error('获取设置失败:', error);
        throw error;
      }
    });

    // 更新设置
    ipcMain.handle('update-settings', async (event, updates) => {
      try {
        await this.dataManager.updateSettings(updates);
        
        // 通知所有窗口设置已更新
        const analysisWindow = this.windowManager.getAnalysisWindow();
        if (analysisWindow && !analysisWindow.isDestroyed()) {
          const newSettings = await this.dataManager.getSettings();
          analysisWindow.webContents.send('settings-updated', newSettings);
        }
      } catch (error) {
        console.error('更新设置失败:', error);
        throw error;
      }
    });
  }

  /**
   * 设置文本分析相关的IPC处理器
   */
  private setupAnalysisHandlers(): void {
    // 分析文本
    ipcMain.handle('analyze-text', async (event, text: string): Promise<ExtractedEvent[]> => {
      try {
        return await this.textAnalyzer.analyzeText(text);
      } catch (error) {
        console.error('文本分析失败:', error);
        throw error;
      }
    });
  }

  /**
   * 设置窗口相关的IPC处理器
   */
  private setupWindowHandlers(): void {
    // 关闭窗口
    ipcMain.handle('close-window', async (event, windowId: string) => {
      try {
        this.windowManager.closeWindow(windowId);
      } catch (error) {
        console.error('关闭窗口失败:', error);
        throw error;
      }
    });

    // 最小化窗口
    ipcMain.handle('minimize-window', async (event, windowId: string) => {
      try {
        this.windowManager.minimizeWindow(windowId);
      } catch (error) {
        console.error('最小化窗口失败:', error);
        throw error;
      }
    });

    // 切换置顶状态
    ipcMain.handle('toggle-always-on-top', async (event, windowId: string): Promise<boolean> => {
      try {
        return this.windowManager.toggleAlwaysOnTop(windowId);
      } catch (error) {
        console.error('切换置顶状态失败:', error);
        throw error;
      }
    });

    // 打开分析窗口
    ipcMain.handle('open-analysis-window', async () => {
      try {
        await this.windowManager.createAnalysisWindow();
      } catch (error) {
        console.error('打开分析窗口失败:', error);
        throw error;
      }
    });

    // 打开剪贴板监控窗口
    ipcMain.handle('open-clipboard-window', async () => {
      try {
        await this.windowManager.createClipboardWindow();
      } catch (error) {
        console.error('打开剪贴板监控窗口失败:', error);
        throw error;
      }
    });
  }

  /**
   * 设置系统相关的IPC处理器
   */
  private setupSystemHandlers(): void {
    // 复制到剪贴板
    ipcMain.handle('copy-to-clipboard', async (event, text: string) => {
      try {
        clipboard.writeText(text);
      } catch (error) {
        console.error('复制到剪贴板失败:', error);
        throw error;
      }
    });

    
  }
}