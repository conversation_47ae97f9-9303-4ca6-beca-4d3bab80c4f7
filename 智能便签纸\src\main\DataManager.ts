/**
 * 数据管理器 - 负责数据的持久化存储和管理
 */

import Store from 'electron-store';
import { v4 as uuidv4 } from 'uuid';
import { 
  AppData, 
  NoteData, 
  AppSettings, 
  DEFAULT_SETTINGS,
  DEFAULT_NOTE_SIZE,
  DEFAULT_NOTE_POSITION 
} from '../types/index';
import { validateNoteData, validateAppSettings, sanitizeNoteData } from '../utils/validation';

export class DataManager {
  private store: Store<AppData>;
  private initialized = false;

  constructor() {
    // 初始化electron-store
    this.store = new Store<AppData>({
      name: 'smart-chat-notes-data',
      defaults: {
        notes: [],
        settings: DEFAULT_SETTINGS
      },
      // 数据验证
      schema: {
        notes: {
          type: 'array',
          items: {
            type: 'object'
          }
        },
        settings: {
          type: 'object'
        }
      }
    });
  }

  /**
   * 初始化数据管理器
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // 验证现有数据
      await this.validateAndCleanData();
      this.initialized = true;
      console.log('数据管理器初始化成功');
    } catch (error) {
      console.error('数据管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 创建新便签
   */
  async createNote(noteData: Omit<NoteData, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date().toISOString();
    const id = uuidv4();

    const newNote: NoteData = {
      id,
      createdAt: now,
      updatedAt: now,
      ...noteData
    };

    // 验证数据
    if (!validateNoteData(newNote)) {
      throw new Error('无效的便签数据');
    }

    // 保存到存储
    const notes = this.store.get('notes', []);
    notes.push(newNote);
    this.store.set('notes', notes);

    console.log(`创建便签成功: ${id}`);
    return id;
  }

  /**
   * 更新便签
   */
  async updateNote(noteId: string, updates: Partial<NoteData>): Promise<void> {
    const notes = this.store.get('notes', []);
    const noteIndex = notes.findIndex(note => note.id === noteId);

    if (noteIndex === -1) {
      throw new Error(`便签不存在: ${noteId}`);
    }

    // 清理和验证更新数据
    const sanitizedUpdates = sanitizeNoteData(updates);
    
    // 更新便签
    const updatedNote = {
      ...notes[noteIndex],
      ...sanitizedUpdates,
      updatedAt: new Date().toISOString()
    };

    // 验证更新后的数据
    if (!validateNoteData(updatedNote)) {
      throw new Error('更新后的便签数据无效');
    }

    notes[noteIndex] = updatedNote;
    this.store.set('notes', notes);

    console.log(`更新便签成功: ${noteId}`);
  }

  /**
   * 删除便签
   */
  async deleteNote(noteId: string): Promise<void> {
    const notes = this.store.get('notes', []);
    const filteredNotes = notes.filter(note => note.id !== noteId);

    if (filteredNotes.length === notes.length) {
      throw new Error(`便签不存在: ${noteId}`);
    }

    this.store.set('notes', filteredNotes);
    console.log(`删除便签成功: ${noteId}`);
  }

  /**
   * 标记便签为完成
   */
  async completeNote(noteId: string): Promise<void> {
    await this.updateNote(noteId, { 
      isCompleted: true,
      updatedAt: new Date().toISOString()
    });
  }

  /**
   * 获取单个便签
   */
  async getNote(noteId: string): Promise<NoteData | null> {
    const notes = this.store.get('notes', []);
    return notes.find(note => note.id === noteId) || null;
  }

  /**
   * 获取所有便签
   */
  async getAllNotes(): Promise<NoteData[]> {
    return this.store.get('notes', []);
  }

  /**
   * 获取活跃便签（未完成的）
   */
  async getActiveNotes(): Promise<NoteData[]> {
    const notes = await this.getAllNotes();
    return notes.filter(note => !note.isCompleted);
  }

  /**
   * 获取应用设置
   */
  async getSettings(): Promise<AppSettings> {
    return this.store.get('settings', DEFAULT_SETTINGS);
  }

  /**
   * 更新应用设置
   */
  async updateSettings(updates: Partial<AppSettings>): Promise<void> {
    const currentSettings = await this.getSettings();
    const newSettings = { ...currentSettings, ...updates };

    // 验证设置数据
    if (!validateAppSettings(newSettings)) {
      throw new Error('无效的设置数据');
    }

    this.store.set('settings', newSettings);
    console.log('更新设置成功');
  }

  /**
   * 获取存储统计信息
   */
  getStorageStats(): { totalNotes: number; activeNotes: number; completedNotes: number } {
    const notes = this.store.get('notes', []);
    const activeNotes = notes.filter(note => !note.isCompleted);
    const completedNotes = notes.filter(note => note.isCompleted);

    return {
      totalNotes: notes.length,
      activeNotes: activeNotes.length,
      completedNotes: completedNotes.length
    };
  }

  /**
   * 清理已完成的便签（可选的清理操作）
   */
  async cleanupCompletedNotes(olderThanDays: number = 30): Promise<number> {
    const notes = this.store.get('notes', []);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const filteredNotes = notes.filter(note => {
      if (!note.isCompleted) {
        return true; // 保留未完成的便签
      }
      
      const updatedDate = new Date(note.updatedAt);
      return updatedDate > cutoffDate; // 保留最近完成的便签
    });

    const removedCount = notes.length - filteredNotes.length;
    
    if (removedCount > 0) {
      this.store.set('notes', filteredNotes);
      console.log(`清理了 ${removedCount} 个旧的已完成便签`);
    }

    return removedCount;
  }

  /**
   * 备份数据
   */
  async backupData(): Promise<AppData> {
    return {
      notes: await this.getAllNotes(),
      settings: await this.getSettings()
    };
  }

  /**
   * 恢复数据
   */
  async restoreData(data: AppData): Promise<void> {
    // 验证数据
    if (!Array.isArray(data.notes)) {
      throw new Error('无效的备份数据：便签数据格式错误');
    }

    if (!validateAppSettings(data.settings)) {
      throw new Error('无效的备份数据：设置数据格式错误');
    }

    // 验证每个便签
    for (const note of data.notes) {
      if (!validateNoteData(note)) {
        throw new Error(`无效的便签数据: ${(note as any)?.id || '未知ID'}`);
      }
    }

    // 恢复数据
    this.store.set('notes', data.notes);
    this.store.set('settings', data.settings);

    console.log(`恢复数据成功: ${data.notes.length} 个便签`);
  }

  /**
   * 验证和清理现有数据
   */
  private async validateAndCleanData(): Promise<void> {
    try {
      const notes = this.store.get('notes', []);
      const settings = this.store.get('settings', DEFAULT_SETTINGS);

      // 验证和清理便签数据
      const validNotes = notes.filter(note => {
        if (!validateNoteData(note)) {
          console.warn(`发现无效便签数据，已移除: ${(note as any)?.id || '未知ID'}`);
          return false;
        }
        return true;
      });

      // 验证设置数据
      let validSettings = settings;
      if (!validateAppSettings(settings)) {
        console.warn('发现无效设置数据，使用默认设置');
        validSettings = DEFAULT_SETTINGS;
      }

      // 如果有数据被清理，更新存储
      if (validNotes.length !== notes.length) {
        this.store.set('notes', validNotes);
        console.log(`清理了 ${notes.length - validNotes.length} 个无效便签`);
      }

      if (validSettings !== settings) {
        this.store.set('settings', validSettings);
      }

    } catch (error) {
      console.error('数据验证和清理失败:', error);
      // 如果数据严重损坏，重置为默认状态
      this.store.clear();
      this.store.set('notes', []);
      this.store.set('settings', DEFAULT_SETTINGS);
      console.log('数据已重置为默认状态');
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    // 执行最后的数据保存和清理工作
    console.log('数据管理器清理完成');
  }
}