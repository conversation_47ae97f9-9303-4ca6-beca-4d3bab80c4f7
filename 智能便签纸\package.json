{"name": "smart-chat-notes", "version": "1.0.0", "description": "智聊便签 - 智能文本分析桌面便签应用", "main": "dist/main/main.js", "homepage": ".", "scripts": {"start": "concurrently \"npm run build:watch\" \"npm run electron:dev\"", "build": "npm run build:main && npm run build:renderer", "build:main": "node_modules\\.bin\\tsc.cmd -p tsconfig.main.json", "build:renderer": "webpack --config webpack.config.js --mode production", "build:watch": "concurrently \"npm run build:main:watch\" \"npm run build:renderer:watch\"", "build:main:watch": "tsc -p tsconfig.main.json --watch", "build:renderer:watch": "webpack --config webpack.config.js --mode development --watch", "electron:dev": "wait-on dist/main/main.js && electron dist/main/main.js", "electron:pack": "electron-builder", "preelectron:pack": "npm run build", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["electron", "desktop", "notes", "ai", "productivity"], "author": "智聊便签开发团队", "license": "MIT", "devDependencies": {"@types/node": "^20.5.0", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^8.41.0", "@typescript-eslint/parser": "^8.41.0", "concurrently": "^8.2.0", "copy-webpack-plugin": "^13.0.1", "css-loader": "^6.8.1", "electron": "^25.6.0", "electron-builder": "^24.6.3", "eslint": "^9.34.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "html-webpack-plugin": "^5.5.3", "jest": "^29.6.2", "style-loader": "^3.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.4.4", "typescript": "^5.1.6", "typescript-eslint": "^8.41.0", "wait-on": "^7.0.1", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "dependencies": {"@nut-tree/nut-js": "^3.1.2", "bootstrap": "^5.3.1", "chrono-node": "^2.7.0", "electron-store": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.0"}, "build": {"appId": "com.smartchatnotes.app", "productName": "智聊便签", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}