<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智聊便签 - 分析面板</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .main-container {
            height: 100vh;
            padding: 20px;
        }
        .analysis-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .input-section {
            flex: 0 0 60%;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        .control-section {
            flex: 0 0 auto;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            background-color: #f8f9fa;
        }
        .result-section {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        .input-textarea {
            height: 100%;
            resize: none;
            border: 1px solid #dee2e6;
            border-radius: 6px;
        }
        .input-textarea:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        .event-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 15px;
            background: white;
            transition: all 0.2s ease;
        }
        .event-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .confidence-badge {
            font-size: 0.75rem;
        }
        .btn-analyze {
            min-width: 120px;
        }
        .loading-spinner {
            width: 1rem;
            height: 1rem;
        }
    </style>
</head>
<body>
    <div id="root" class="h-100"></div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>