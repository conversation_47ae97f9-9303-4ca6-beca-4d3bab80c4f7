<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>剪贴板监控 - 智聊便签</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin-top: 2rem;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-active {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        .status-inactive {
            background-color: #dc3545;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .clipboard-content {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #667eea;
            max-height: 200px;
            overflow-y: auto;
        }
        .event-item {
            background-color: #e3f2fd;
            border-radius: 8px;
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-left: 4px solid #2196f3;
        }
        .btn-custom {
            border-radius: 25px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
        }
        .log-area {
            background-color: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Consolas', monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-clipboard-data me-2"></i>
                    剪贴板监控
                </h4>
                <p class="mb-0 mt-2 opacity-75">自动监控剪贴板内容，提取时间相关事件并创建便签提醒</p>
            </div>
            <div class="card-body">
                <!-- 监控状态 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-3">
                            <span class="status-indicator" id="statusIndicator"></span>
                            <span id="statusText">未启动</span>
                        </div>
                        <button id="toggleBtn" class="btn btn-primary btn-custom">
                            <i class="bi bi-play-fill me-1"></i>
                            启动监控
                        </button>
                        <button id="analyzeBtn" class="btn btn-outline-secondary btn-custom ms-2">
                            <i class="bi bi-search me-1"></i>
                            分析当前剪贴板
                        </button>
                    </div>
                    <div class="col-md-6">
                        <div class="text-end">
                            <small class="text-muted">使用说明：复制包含时间信息的文本到剪贴板</small>
                        </div>
                    </div>
                </div>

                <!-- 当前剪贴板内容 -->
                <div class="mb-4">
                    <h6><i class="bi bi-clipboard me-2"></i>当前剪贴板内容</h6>
                    <div class="clipboard-content" id="clipboardContent">
                        <em class="text-muted">暂无内容</em>
                    </div>
                </div>

                <!-- 提取的事件 -->
                <div class="mb-4">
                    <h6><i class="bi bi-calendar-event me-2"></i>提取的事件</h6>
                    <div id="extractedEvents">
                        <em class="text-muted">暂无提取的事件</em>
                    </div>
                </div>

                <!-- 监控日志 -->
                <div class="mb-3">
                    <h6><i class="bi bi-terminal me-2"></i>监控日志</h6>
                    <div class="log-area" id="logArea">
                        <div>等待启动监控...</div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="text-end">
                    <button id="clearLogBtn" class="btn btn-outline-warning btn-custom">
                        <i class="bi bi-trash me-1"></i>
                        清空日志
                    </button>
                    <button id="closeBtn" class="btn btn-outline-secondary btn-custom ms-2">
                        <i class="bi bi-x-lg me-1"></i>
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="clipboard.js"></script>
</body>
</html>