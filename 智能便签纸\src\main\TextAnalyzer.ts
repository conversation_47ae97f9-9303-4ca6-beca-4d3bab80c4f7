/**
 * 文本分析器 - 使用chrono-node进行智能文本分析
 */

import * as chrono from 'chrono-node';
import { ExtractedEvent } from '../types/index';

export class TextAnalyzer {
  private chronoParser: any;

  constructor() {
    // 配置chrono-node解析器
    this.chronoParser = chrono.casual.clone();
    
    // 可以在这里添加自定义的解析规则
    this.setupCustomParsers();
  }

  /**
   * 分析文本并提取事件
   */
  async analyzeText(text: string): Promise<ExtractedEvent[]> {
    if (!text || text.trim().length === 0) {
      return [];
    }

    try {
      const events: ExtractedEvent[] = [];
      
      // 使用chrono-node解析时间表达式
      const parseResults = this.chronoParser.parse(text, new Date(), { forwardDate: true });
      
      for (const result of parseResults) {
        const event = this.extractEventFromResult(result, text);
        if (event) {
          events.push(event);
        }
      }

      // 如果没有找到时间表达式，尝试提取其他可能的事件
      if (events.length === 0) {
        const fallbackEvents = this.extractFallbackEvents(text);
        events.push(...fallbackEvents);
      }

      return events;
    } catch (error) {
      console.error('文本分析失败:', error);
      return [];
    }
  }

  /**
   * 从chrono解析结果中提取事件
   */
  private extractEventFromResult(result: any, originalText: string): ExtractedEvent | null {
    try {
      const timestamp = result.start.date();
      const timeExpression = result.text;
      
      // 提取事件描述（时间表达式前后的文本）
      const description = this.extractEventDescription(originalText, result.index, result.text);
      
      // 生成事件标题
      const title = this.generateEventTitle(description, timeExpression);
      
      // 计算置信度
      const confidence = this.calculateConfidence(result, description);

      return {
        title,
        description,
        timestamp,
        confidence,
        originalText: originalText.substring(
          Math.max(0, result.index - 20),
          Math.min(originalText.length, result.index + result.text.length + 20)
        ),
        timeExpression
      };
    } catch (error) {
      console.error('提取事件失败:', error);
      return null;
    }
  }

  /**
   * 提取事件描述
   */
  private extractEventDescription(text: string, timeIndex: number, timeText: string): string {
    const beforeTime = text.substring(0, timeIndex).trim();
    const afterTime = text.substring(timeIndex + timeText.length).trim();
    
    // 提取时间前后的相关文本
    const beforeWords = beforeTime.split(/\s+/).slice(-10).join(' ');
    const afterWords = afterTime.split(/\s+/).slice(0, 10).join(' ');
    
    // 组合描述
    let description = '';
    if (beforeWords) {
      description += beforeWords;
    }
    if (afterWords) {
      if (description) description += ' ';
      description += afterWords;
    }
    
    return description.trim() || '未知事件';
  }

  /**
   * 生成事件标题
   */
  private generateEventTitle(description: string, timeExpression: string): string {
    // 提取关键动词和名词
    const keywords = this.extractKeywords(description);
    
    if (keywords.length > 0) {
      return keywords.slice(0, 3).join(' ');
    }
    
    // 如果没有找到关键词，使用描述的前几个字
    const words = description.split(/\s+/);
    return words.slice(0, 3).join(' ') || `${timeExpression}的事件`;
  }

  /**
   * 提取关键词
   */
  private extractKeywords(text: string): string[] {
    // 常见的事件关键词
    const eventKeywords = [
      '会议', '聚餐', '约会', '面试', '考试', '交付', '提交', '完成',
      '开会', '讨论', '汇报', '演示', '培训', '学习', '复习',
      '出差', '旅行', '回家', '上班', '下班', '休假',
      '生日', '纪念日', '节日', '庆祝', '聚会',
      '看电影', '运动', '健身', '购物', '吃饭'
    ];
    
    const keywords: string[] = [];
    
    for (const keyword of eventKeywords) {
      if (text.includes(keyword)) {
        keywords.push(keyword);
      }
    }
    
    return keywords;
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(result: any, description: string): number {
    let confidence = 0.5; // 基础置信度
    
    // 根据时间解析的确定性调整
    if (result.start.isCertain('year')) confidence += 0.1;
    if (result.start.isCertain('month')) confidence += 0.1;
    if (result.start.isCertain('day')) confidence += 0.1;
    if (result.start.isCertain('hour')) confidence += 0.1;
    
    // 根据描述的质量调整
    if (description.length > 10) confidence += 0.1;
    if (this.extractKeywords(description).length > 0) confidence += 0.1;
    
    return Math.min(1.0, confidence);
  }

  /**
   * 提取备用事件（当没有找到时间表达式时）
   */
  private extractFallbackEvents(text: string): ExtractedEvent[] {
    const events: ExtractedEvent[] = [];
    
    // 查找可能的事件关键词
    const eventPatterns = [
      /(?:记得|别忘了|提醒我)(.{1,20})/g,
      /(?:要|需要|得)(.{1,20})/g,
      /(.{1,20})(?:很重要|重要|紧急)/g
    ];
    
    for (const pattern of eventPatterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const description = match[1].trim();
        if (description.length > 2) {
          events.push({
            title: description.substring(0, 10),
            description,
            timestamp: null,
            confidence: 0.3,
            originalText: match[0],
            timeExpression: undefined
          });
        }
      }
    }
    
    return events;
  }

  /**
   * 同步版本的事件提取（用于向后兼容）
   */
  extractEvents(text: string): ExtractedEvent[] {
    if (!text || text.trim().length === 0) {
      return [];
    }

    try {
      const events: ExtractedEvent[] = [];
      
      // 使用chrono-node解析时间表达式
      const results = this.chronoParser.parse(text, new Date());
      
      for (const result of results) {
        if (result.start && result.start.date()) {
          const event: ExtractedEvent = {
            title: this.generateEventTitle(text, result),
            description: this.extractEventDescription(text, 0, result.text),
            timestamp: result.start.date(),
            originalText: result.text,
            confidence: this.calculateConfidence(result, text),
            timeExpression: result.text
          };
          
          events.push(event);
        }
      }

      return events;
    } catch (error) {
      console.error('同步文本分析失败:', error);
      return [];
    }
  }

  /**
   * 设置自定义解析器
   */
  private setupCustomParsers(): void {
    // 可以在这里添加中文特定的时间表达式解析规则
    // 例如："后天"、"大后天"、"下下周"等
    
    // 这里可以扩展chrono-node的解析能力
    // 目前使用默认配置
  }
}