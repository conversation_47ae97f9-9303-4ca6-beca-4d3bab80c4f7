/**
 * 智聊便签主进程入口
 */

import { app, BrowserWindow, ipcMain, Menu } from 'electron';
import * as path from 'path';
import { WindowManager } from './WindowManager';
import { DataManager } from './DataManager';
import { IPCHandler } from './IPCHandler';
import { TextAnalyzer } from './TextAnalyzer';


import { SuggestionManager } from './SuggestionManager';
import { ClipboardMonitor } from './ClipboardMonitor';

// 保持对窗口对象的全局引用，避免被垃圾回收
let windowManager: WindowManager;
let dataManager: DataManager;
let ipcHandler: IPCHandler;
let textAnalyzer: TextAnalyzer;
let suggestionManager: SuggestionManager;
let clipboardMonitor: ClipboardMonitor;


/**
 * 应用准备就绪时的处理
 */
async function createApplication(): Promise<void> {
  try {
    // 初始化数据管理器
    dataManager = new DataManager();
    await dataManager.initialize();

    // 初始化文本分析器
    textAnalyzer = new TextAnalyzer();

    // 初始化窗口管理器
    windowManager = new WindowManager();

    // 初始化IPC处理器
    ipcHandler = new IPCHandler(windowManager, dataManager);
    ipcHandler.setupHandlers();

    // 初始化智能剪贴板相关服务
    suggestionManager = new SuggestionManager(dataManager, windowManager);
    clipboardMonitor = new ClipboardMonitor(textAnalyzer, suggestionManager);
    clipboardMonitor.startMonitoring();

    

    // 监听自动创建便签事件
    ipcMain.on('auto-create-note', async (event: any, noteData: any) => {
      try {
        const noteId = await dataManager.createNote(noteData);
        const createdNote = await dataManager.getNote(noteId);
        if (createdNote) {
          await windowManager.createNoteWindow(createdNote);
          console.log('自动创建便签成功:', createdNote.id);
        }
      } catch (error) {
        console.error('自动创建便签失败:', error);
      }
    });

    // 创建主窗口
    await windowManager.createMainWindow();

    // 恢复之前的便签窗口
    await windowManager.restoreNoteWindows();

    console.log('智聊便签应用启动成功');
  } catch (error) {
    console.error('应用启动失败:', error);
    app.quit();
  }
}

/**
 * 应用初始化
 */
function initializeApp(): void {
  // 设置应用用户模型ID (Windows)
  if (process.platform === 'win32') {
    app.setAppUserModelId('com.smartchatnotes.app');
  }

  // 设置应用菜单
  setupApplicationMenu();

  // 开发环境下启用开发者工具
  if (process.env.NODE_ENV === 'development') {
    // 安装开发者扩展
    installDevExtensions();
  }
}

/**
 * 设置应用菜单
 */
function setupApplicationMenu(): void {
  const template: Electron.MenuItemConstructorOptions[] = [
    {
      label: '文件',
      submenu: [
        {
          label: '新建便签',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // TODO: 实现新建便签功能
          }
        },
        { type: 'separator' },
        {
          label: '退出',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: '编辑',
      submenu: [
        { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: '窗口',
      submenu: [
        { label: '最小化', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: '关闭', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: '帮助',
      submenu: [
        {
          label: '关于智聊便签',
          click: () => {
            // TODO: 显示关于对话框
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

/**
 * 安装开发者扩展
 */
async function installDevExtensions(): Promise<void> {
  try {
    // 开发环境下可以安装开发者工具
    console.log('开发者工具安装跳过');
  } catch (error) {
    console.log('开发者工具安装失败:', error);
  }
}

// 应用事件处理
app.whenReady().then(() => {
  initializeApp();
  createApplication();

  // macOS特殊处理：当dock图标被点击且没有其他窗口打开时，重新创建窗口
  app.on('activate', async () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      await createApplication();
    }
  });
});

// 当所有窗口都被关闭时退出应用（macOS除外）
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 应用即将退出时的清理工作
app.on('before-quit', async (event) => {
  if (windowManager) {
    event.preventDefault();
    try {
      
      await windowManager.saveAllWindowStates();
      await dataManager.cleanup();
      app.exit(0);
    } catch (error) {
      console.error('应用退出清理失败:', error);
      app.exit(1);
    }
  }
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  // 在生产环境中，可能需要更优雅的错误处理
  if (process.env.NODE_ENV === 'production') {
    app.quit();
  }
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason, 'at:', promise);
});

// 导出主要对象供测试使用
export { windowManager, dataManager, ipcHandler };