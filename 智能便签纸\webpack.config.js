const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyPlugin = require('copy-webpack-plugin');

module.exports = {
  entry: {
        analysis: './src/renderer/analysis/index.tsx',
        note: './src/renderer/note/index.tsx',
        suggestion: './src/renderer/suggestion/suggestion.tsx',
    },
  output: {
    path: path.resolve(__dirname, 'dist/renderer'),
    filename: '[name]/bundle.js',
    clean: true
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: 'ts-loader',
        exclude: /node_modules/
      },
      {
        test: /\.css$/i,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.(png|svg|jpg|jpeg|gif|ico)$/i,
        type: 'asset/resource'
      }
    ]
  },
  resolve: {
    extensions: ['.tsx', '.ts', '.js'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@main': path.resolve(__dirname, 'src/main'),
      '@renderer': path.resolve(__dirname, 'src/renderer'),
      '@types': path.resolve(__dirname, 'src/types'),
      '@utils': path.resolve(__dirname, 'src/utils')
    }
  },
  plugins: [
    new HtmlWebpackPlugin({
            template: './src/renderer/analysis/index.html',
            filename: 'analysis/index.html',
            chunks: ['analysis'],
        }),
    new HtmlWebpackPlugin({
      template: './src/renderer/note/index.html',
      filename: 'note/index.html',
      chunks: ['note']
    }),
    new HtmlWebpackPlugin({
      template: './src/renderer/suggestion/suggestion.html',
      filename: 'suggestion/suggestion.html',
      chunks: ['suggestion']
    }),
    new CopyPlugin({
      patterns: [
        {
          from: 'src/renderer/main/main.html',
          to: 'main/main.html'
        }
      ]
    })
  ],
  target: 'electron-renderer',
  devtool: 'source-map'
};