"use strict";
/**
 * 智聊便签应用数据类型定义
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ANALYSIS_WINDOW_CONFIG = exports.DEFAULT_NOTE_POSITION = exports.DEFAULT_NOTE_SIZE = exports.DEFAULT_SETTINGS = exports.NOTE_COLORS = void 0;
/**
 * 便签颜色配置
 */
exports.NOTE_COLORS = {
    yellow: { bg: '#FFFFE0', border: '#F0E68C', text: '#8B4513' },
    blue: { bg: '#E6F3FF', border: '#87CEEB', text: '#2F4F4F' },
    green: { bg: '#F0FFF0', border: '#90EE90', text: '#006400' },
    pink: { bg: '#FFE4E1', border: '#FFC0CB', text: '#8B008B' },
    orange: { bg: '#FFF8DC', border: '#FFB347', text: '#FF4500' }
};
/**
 * 默认配置常量
 */
exports.DEFAULT_SETTINGS = {
    launchAtLogin: false,
    defaultNoteColor: 'yellow'
};
exports.DEFAULT_NOTE_SIZE = {
    width: 220,
    height: 180
};
exports.DEFAULT_NOTE_POSITION = {
    x: 100,
    y: 100
};
exports.ANALYSIS_WINDOW_CONFIG = {
    width: 600,
    height: 400,
    minWidth: 500,
    minHeight: 350
};
//# sourceMappingURL=index.js.map