/**
 * 智聊便签应用数据类型定义
 */
/**
 * 便签数据结构
 */
export interface NoteData {
    /** 便签唯一标识符 */
    id: string;
    /** 事件描述内容 */
    content: string;
    /** 事件时间戳 (ISO 8601格式) */
    eventTimestamp: string;
    /** 便签窗口位置 */
    position: {
        x: number;
        y: number;
    };
    /** 便签窗口大小 */
    size: {
        width: number;
        height: number;
    };
    /** 便签颜色 */
    color: NoteColor;
    /** 是否置顶 */
    isPinned: boolean;
    /** 是否已完成 */
    isCompleted: boolean;
    /** 创建时间 */
    createdAt: string;
    /** 最后更新时间 */
    updatedAt: string;
}
/**
 * 便签颜色枚举
 */
export type NoteColor = 'yellow' | 'blue' | 'green' | 'pink' | 'orange';
/**
 * 便签颜色配置
 */
export declare const NOTE_COLORS: Record<NoteColor, {
    bg: string;
    border: string;
    text: string;
}>;
/**
 * 应用设置
 */
export interface AppSettings {
    /** 开机自启动 */
    launchAtLogin: boolean;
    /** 默认便签颜色 */
    defaultNoteColor: NoteColor;
    /** 分析窗口位置 */
    analysisWindowPosition?: {
        x: number;
        y: number;
    };
    /** 分析窗口大小 */
    analysisWindowSize?: {
        width: number;
        height: number;
    };
}
/**
 * 应用数据结构
 */
export interface AppData {
    /** 便签列表 */
    notes: NoteData[];
    /** 应用设置 */
    settings: AppSettings;
}
/**
 * 文本分析提取的事件
 */
export interface ExtractedEvent {
    /** 事件标题 */
    title: string;
    /** 事件描述 */
    description: string;
    /** 解析出的时间 */
    timestamp: Date | null;
    /** 解析置信度 (0-1) */
    confidence: number;
    /** 原始文本片段 */
    originalText: string;
    /** 时间表达式 */
    timeExpression?: string;
}
/**
 * 窗口状态
 */
export interface WindowState {
    /** 窗口ID */
    id: string;
    /** 窗口类型 */
    type: 'analysis' | 'note';
    /** 窗口位置 */
    position: {
        x: number;
        y: number;
    };
    /** 窗口大小 */
    size: {
        width: number;
        height: number;
    };
    /** 是否可见 */
    isVisible: boolean;
    /** 是否置顶 */
    isAlwaysOnTop: boolean;
}
/**
 * IPC通信消息类型
 */
export interface IPCMessages {
    'note-data-updated': (noteData: NoteData) => void;
    'settings-updated': (settings: AppSettings) => void;
    'analysis-result': (events: ExtractedEvent[]) => void;
    'create-note': (noteData: Omit<NoteData, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>;
    'update-note': (noteId: string, updates: Partial<NoteData>) => Promise<void>;
    'delete-note': (noteId: string) => Promise<void>;
    'complete-note': (noteId: string) => Promise<void>;
    'get-all-notes': () => Promise<NoteData[]>;
    'get-settings': () => Promise<AppSettings>;
    'update-settings': (settings: Partial<AppSettings>) => Promise<void>;
    'analyze-text': (text: string) => Promise<ExtractedEvent[]>;
    'copy-to-clipboard': (text: string) => Promise<void>;
    'close-window': (windowId: string) => Promise<void>;
    'minimize-window': (windowId: string) => Promise<void>;
    'toggle-always-on-top': (windowId: string) => Promise<boolean>;
}
/**
 * 默认配置常量
 */
export declare const DEFAULT_SETTINGS: AppSettings;
export declare const DEFAULT_NOTE_SIZE: {
    width: number;
    height: number;
};
export declare const DEFAULT_NOTE_POSITION: {
    x: number;
    y: number;
};
export declare const ANALYSIS_WINDOW_CONFIG: {
    width: number;
    height: number;
    minWidth: number;
    minHeight: number;
};
/**
 * 事件卡片状态
 */
export interface EventCardState {
    /** 事件数据 */
    event: ExtractedEvent;
    /** 是否正在编辑 */
    isEditing: boolean;
    /** 编辑后的标题 */
    editedTitle: string;
    /** 编辑后的时间 */
    editedTimestamp: Date | null;
    /** 是否已忽略 */
    isIgnored: boolean;
}
/**
 * 分析面板状态
 */
export interface AnalysisPanelState {
    /** 输入文本 */
    inputText: string;
    /** 是否正在分析 */
    isAnalyzing: boolean;
    /** 分析结果 */
    events: EventCardState[];
    /** 错误信息 */
    error: string | null;
}
/**
 * 便签窗口状态
 */
export interface NoteWindowState {
    /** 便签数据 */
    noteData: NoteData;
    /** 是否显示控制面板 */
    showControls: boolean;
    /** 是否正在拖拽 */
    isDragging: boolean;
    /** 拖拽起始位置 */
    dragStart: {
        x: number;
        y: number;
    } | null;
}
