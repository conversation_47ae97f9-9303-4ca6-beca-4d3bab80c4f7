# 智聊便签 (Smart Chat Notes)

一款智能的桌面便签应用，能够自动分析聊天记录中的重要事件并生成桌面提醒便签。

## 功能特性

- 🔍 **智能文本分析**: 自动识别聊天记录中的时间和事件信息
- 📝 **桌面便签**: 无边框置顶便签窗口，持续提醒重要事项
- 🎨 **个性化定制**: 支持便签颜色切换和位置记忆
- 📋 **便签分享**: 一键复制便签内容到剪贴板
- 💾 **数据持久化**: 本地存储，应用重启后自动恢复便签

## 技术栈

- **应用框架**: Electron
- **前端**: React 18 + TypeScript
- **UI框架**: Bootstrap 5
- **文本分析**: chrono-node
- **数据存储**: electron-store

## 开发环境

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm start
```

### 构建应用

```bash
npm run build
```

### 打包应用

```bash
npm run electron:pack
```

## 项目结构

```
smart-chat-notes/
├── src/
│   ├── main/           # 主进程代码
│   ├── renderer/       # 渲染进程代码
│   │   ├── analysis/   # 分析面板
│   │   └── note/       # 便签窗口
│   ├── types/          # TypeScript类型定义
│   └── utils/          # 工具函数
├── assets/             # 静态资源
├── docs/               # 项目文档
└── dist/               # 构建输出
```

## 使用说明

1. 启动应用，打开分析面板
2. 将聊天记录粘贴到文本框中
3. 点击"智能分析"按钮
4. 查看识别出的事件，编辑标题和时间
5. 点击"创建便签"生成桌面提醒
6. 便签将持续显示在桌面上，直到标记完成

## 开发文档

- [项目对齐文档](docs/智聊便签/ALIGNMENT_智聊便签.md)
- [系统架构设计](docs/智聊便签/DESIGN_智聊便签.md)
- [任务分解计划](docs/智聊便签/TASK_智聊便签.md)

## 许可证

MIT License