/**
 * 数据验证工具函数
 */

import { NoteData, AppSettings, ExtractedEvent, NoteColor } from '../types/index';

/**
 * 验证便签数据
 */
export function validateNoteData(data: any): data is NoteData {
  if (!data || typeof data !== 'object') {
    return false;
  }

  const required = ['id', 'content', 'eventTimestamp', 'position', 'size', 'color', 'createdAt', 'updatedAt'];
  for (const field of required) {
    if (!(field in data)) {
      return false;
    }
  }

  // 验证ID
  if (typeof data.id !== 'string' || data.id.length === 0) {
    return false;
  }

  // 验证内容
  if (typeof data.content !== 'string') {
    return false;
  }

  // 验证时间戳
  if (typeof data.eventTimestamp !== 'string' || !isValidISOString(data.eventTimestamp)) {
    return false;
  }

  // 验证位置
  if (!isValidPosition(data.position)) {
    return false;
  }

  // 验证大小
  if (!isValidSize(data.size)) {
    return false;
  }

  // 验证颜色
  if (!isValidNoteColor(data.color)) {
    return false;
  }

  // 验证布尔值
  if (typeof data.isPinned !== 'boolean' || typeof data.isCompleted !== 'boolean') {
    return false;
  }

  // 验证创建和更新时间
  if (!isValidISOString(data.createdAt) || !isValidISOString(data.updatedAt)) {
    return false;
  }

  return true;
}

/**
 * 验证应用设置
 */
export function validateAppSettings(data: any): data is AppSettings {
  if (!data || typeof data !== 'object') {
    return false;
  }

  if (typeof data.launchAtLogin !== 'boolean') {
    return false;
  }

  if (!isValidNoteColor(data.defaultNoteColor)) {
    return false;
  }

  // 可选字段验证
  if (data.analysisWindowPosition && !isValidPosition(data.analysisWindowPosition)) {
    return false;
  }

  if (data.analysisWindowSize && !isValidSize(data.analysisWindowSize)) {
    return false;
  }

  return true;
}

/**
 * 验证提取的事件
 */
export function validateExtractedEvent(data: any): data is ExtractedEvent {
  if (!data || typeof data !== 'object') {
    return false;
  }

  const required = ['title', 'description', 'confidence', 'originalText'];
  for (const field of required) {
    if (!(field in data)) {
      return false;
    }
  }

  if (typeof data.title !== 'string' || typeof data.description !== 'string') {
    return false;
  }

  if (typeof data.confidence !== 'number' || data.confidence < 0 || data.confidence > 1) {
    return false;
  }

  if (typeof data.originalText !== 'string') {
    return false;
  }

  // timestamp可以为null或Date
  if (data.timestamp !== null && !(data.timestamp instanceof Date)) {
    return false;
  }

  return true;
}

/**
 * 验证位置对象
 */
function isValidPosition(position: any): boolean {
  return (
    position &&
    typeof position === 'object' &&
    typeof position.x === 'number' &&
    typeof position.y === 'number' &&
    !isNaN(position.x) &&
    !isNaN(position.y)
  );
}

/**
 * 验证大小对象
 */
function isValidSize(size: any): boolean {
  return (
    size &&
    typeof size === 'object' &&
    typeof size.width === 'number' &&
    typeof size.height === 'number' &&
    size.width > 0 &&
    size.height > 0 &&
    !isNaN(size.width) &&
    !isNaN(size.height)
  );
}

/**
 * 验证便签颜色
 */
function isValidNoteColor(color: any): color is NoteColor {
  const validColors: NoteColor[] = ['yellow', 'blue', 'green', 'pink', 'orange'];
  return typeof color === 'string' && validColors.includes(color as NoteColor);
}

/**
 * 验证ISO时间字符串
 */
function isValidISOString(dateString: string): boolean {
  try {
    const date = new Date(dateString);
    return date.toISOString() === dateString;
  } catch {
    return false;
  }
}

/**
 * 清理和标准化便签数据
 */
export function sanitizeNoteData(data: Partial<NoteData>): Partial<NoteData> {
  const sanitized: Partial<NoteData> = {};

  if (data.id && typeof data.id === 'string') {
    sanitized.id = data.id.trim();
  }

  if (data.content && typeof data.content === 'string') {
    sanitized.content = data.content.trim();
  }

  if (data.eventTimestamp && typeof data.eventTimestamp === 'string') {
    try {
      const date = new Date(data.eventTimestamp);
      sanitized.eventTimestamp = date.toISOString();
    } catch {
      // 忽略无效时间戳
    }
  }

  if (data.position && isValidPosition(data.position)) {
    sanitized.position = {
      x: Math.round(data.position.x),
      y: Math.round(data.position.y)
    };
  }

  if (data.size && isValidSize(data.size)) {
    sanitized.size = {
      width: Math.max(100, Math.round(data.size.width)),
      height: Math.max(80, Math.round(data.size.height))
    };
  }

  if (data.color && isValidNoteColor(data.color)) {
    sanitized.color = data.color;
  }

  if (typeof data.isPinned === 'boolean') {
    sanitized.isPinned = data.isPinned;
  }

  if (typeof data.isCompleted === 'boolean') {
    sanitized.isCompleted = data.isCompleted;
  }

  return sanitized;
}

/**
 * 生成默认便签数据
 */
export function createDefaultNoteData(overrides: Partial<NoteData> = {}): Omit<NoteData, 'id' | 'createdAt' | 'updatedAt'> {
  const now = new Date().toISOString();
  
  return {
    content: '新便签',
    eventTimestamp: now,
    position: { x: 100, y: 100 },
    size: { width: 220, height: 180 },
    color: 'yellow',
    isPinned: true,
    isCompleted: false,
    ...overrides
  };
}