/**
 * 便签窗口主组件
 */

import React, { useState, useEffect } from 'react';
import type { NoteData } from '../../types/index';
import { NOTE_COLORS } from '../../types/index';

const StickyNote: React.FC = () => {
  const [noteData, setNoteData] = useState<NoteData | null>(null);
  const [showControls, setShowControls] = useState(false);

  useEffect(() => {
    // 监听便签数据
    window.electronAPI.onNoteData((data: NoteData) => {
      setNoteData(data);
    });

    // 监听数据更新
    window.electronAPI.onNoteDataUpdated((data: NoteData) => {
      setNoteData(data);
    });

    return () => {
      window.electronAPI.removeAllListeners('note-data');
      window.electronAPI.removeAllListeners('note-data-updated');
    };
  }, []);

  const handleShare = async () => {
    if (!noteData) return;
    
    const shareText = `【智聊便签 提醒】\n- 事件: ${noteData.content}\n- 时间: ${new Date(noteData.eventTimestamp).toLocaleString('zh-CN')}`;
    
    try {
      await window.electronAPI.copyToClipboard(shareText);
      // 可以添加一个临时提示
    } catch (error) {
      console.error('分享失败:', error);
    }
  };

  const handleTogglePin = async () => {
    if (!noteData) return;
    
    try {
      await window.electronAPI.toggleAlwaysOnTop(noteData.id);
    } catch (error) {
      console.error('切换置顶失败:', error);
    }
  };

  const handleChangeColor = async () => {
    if (!noteData) return;
    
    const colors = Object.keys(NOTE_COLORS) as Array<keyof typeof NOTE_COLORS>;
    const currentIndex = colors.indexOf(noteData.color);
    const nextColor = colors[(currentIndex + 1) % colors.length];
    
    try {
      await window.electronAPI.updateNote(noteData.id, { color: nextColor });
    } catch (error) {
      console.error('更换颜色失败:', error);
    }
  };

  const handleClose = async () => {
    if (!noteData) return;
    
    try {
      await window.electronAPI.completeNote(noteData.id);
    } catch (error) {
      console.error('关闭便签失败:', error);
    }
  };

  if (!noteData) {
    return (
      <div className="d-flex align-items-center justify-content-center h-100">
        <div className="text-muted">加载中...</div>
      </div>
    );
  }

  const colorTheme = NOTE_COLORS[noteData.color];
  const eventTime = new Date(noteData.eventTimestamp);

  return (
    <div
      className="h-100 position-relative p-3 d-flex flex-column"
      style={{
        backgroundColor: colorTheme.bg,
        border: `1px solid ${colorTheme.border}`,
        color: colorTheme.text,
        borderRadius: '8px',
        cursor: 'move'
      }}
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => setShowControls(false)}
    >
      {/* 控制面板 */}
      {showControls && (
        <div
          className="position-absolute d-flex gap-1 p-1"
          style={{
            top: '-30px',
            right: '0',
            backgroundColor: 'rgba(0,0,0,0.8)',
            borderRadius: '4px'
          }}
        >
          <button
            className="btn btn-sm text-white p-1"
            style={{ fontSize: '12px', lineHeight: 1 }}
            onClick={handleShare}
            title="分享"
          >
            📤
          </button>
          <button
            className="btn btn-sm text-white p-1"
            style={{ fontSize: '12px', lineHeight: 1 }}
            onClick={handleTogglePin}
            title={noteData.isPinned ? '取消置顶' : '置顶'}
          >
            📌
          </button>
          <button
            className="btn btn-sm text-white p-1"
            style={{ fontSize: '12px', lineHeight: 1 }}
            onClick={handleChangeColor}
            title="换色"
          >
            🎨
          </button>
          <button
            className="btn btn-sm text-white p-1"
            style={{ fontSize: '12px', lineHeight: 1 }}
            onClick={handleClose}
            title="完成"
          >
            ✓
          </button>
        </div>
      )}

      {/* 便签内容 */}
      <div className="flex-grow-1 d-flex flex-column justify-content-between">
        <div className="fw-bold mb-2" style={{ fontSize: '14px', lineHeight: 1.4 }}>
          {noteData.content}
        </div>
        
        <div className="mt-auto" style={{ fontSize: '12px', opacity: 0.8 }}>
          {eventTime.toLocaleString('zh-CN', {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}
        </div>
      </div>

      {/* 调整大小手柄 */}
      <div
        className="position-absolute"
        style={{
          bottom: '0',
          right: '0',
          width: '12px',
          height: '12px',
          cursor: 'se-resize',
          background: `linear-gradient(-45deg, transparent 0%, transparent 40%, ${colorTheme.text} 40%, ${colorTheme.text} 60%, transparent 60%)`,
          opacity: 0.3
        }}
      />
    </div>
  );
};

export default StickyNote;