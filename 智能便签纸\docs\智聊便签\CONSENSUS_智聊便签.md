# 智聊便签项目共识文档

## 项目概述
**智聊便签 (Smart Chat Notes)** 是一款Electron桌面应用，旨在将即时通讯中的"未来事件"信息自动提取并以桌面便签形式提醒用户。

## 明确的需求描述

### 核心功能需求
1. **分析面板**
   - 主窗口 (600px × 400px)
   - 文本粘贴区域用于输入聊天记录
   - 智能分析按钮触发文本解析
   - 分析结果区域显示事件卡片

2. **智能文本分析**
   - 使用chrono-node解析自然语言时间表达
   - 识别"明天下午3点"、"周五晚上"等时间描述
   - 提取事件内容和上下文信息
   - 生成可编辑的事件卡片

3. **便签式提醒窗口**
   - 无边框、置顶、可拖拽的桌面便签
   - 淡黄色背景 (#FFFFE0)，圆角设计
   - 显示事件标题和格式化时间
   - 悬浮控件：分享、关闭、置顶、调色板

4. **数据持久化**
   - 使用electron-store进行本地JSON存储
   - 应用启动时恢复所有未完成便签
   - 保持便签位置、大小、颜色等状态

5. **便签分享功能**
   - 将便签内容格式化并复制到剪贴板
   - 支持在微信等应用中直接粘贴

### 用户交互流程
1. 用户启动应用，分析面板出现
2. 用户粘贴聊天记录到文本域
3. 点击"智能分析"，系统解析文本
4. 显示事件卡片，用户可编辑标题和时间
5. 点击"创建便签"生成桌面便签
6. 便签持续显示直到用户标记完成或删除

## 技术实现方案

### 技术栈
- **应用框架**: Electron
- **前端**: React 18 + TypeScript
- **UI框架**: Bootstrap
- **状态管理**: React Context + useReducer
- **本地存储**: electron-store
- **NLP引擎**: chrono-node
- **系统交互**: Electron APIs
- **打包工具**: Electron Builder

### 架构设计
```
智聊便签应用
├── 主进程 (Main Process)
│   ├── 应用生命周期管理
│   ├── 窗口管理
│   └── 数据持久化
├── 分析面板 (Renderer Process)
│   ├── 文本输入组件
│   ├── 分析引擎集成
│   └── 事件卡片组件
└── 便签窗口 (Multiple Renderer Processes)
    ├── 便签显示组件
    ├── 交互控件
    └── 状态同步
```

### 数据模型
```json
{
  "notes": [
    {
      "id": "uuid-string",
      "content": "事件描述",
      "eventTimestamp": "iso-timestamp",
      "position": { "x": 1150, "y": 240 },
      "size": { "width": 220, "height": 180 },
      "color": "yellow",
      "isPinned": true,
      "isCompleted": false
    }
  ],
  "settings": { "launchAtLogin": true }
}
```

## 技术约束和集成方案

### 技术约束
1. **平台限制**: 主要支持Windows，后续考虑macOS和Linux
2. **性能要求**: 多便签窗口的内存占用优化
3. **NLP准确性**: chrono-node对复杂中文语境的解析限制
4. **系统集成**: 全局快捷键可能与其他应用冲突

### 集成方案
1. **Electron主进程**: 管理应用生命周期和多窗口
2. **渲染进程通信**: 使用IPC进行主进程和渲染进程通信
3. **数据同步**: 通过主进程统一管理数据状态
4. **窗口管理**: 动态创建和销毁便签窗口

## 任务边界限制

### 包含范围
- 分析面板的完整UI和交互
- 基于chrono-node的文本分析功能
- 便签窗口的创建、显示、交互
- 本地数据存储和恢复
- 便签分享到剪贴板功能
- 基础的便签管理（创建、编辑、删除、完成）

### 不包含范围
- AI对话功能（后续版本）
- 全局快捷键（后续版本）
- 多平台打包（先专注Windows）
- 云端同步功能
- 高级NLP功能
- 移动端支持

## 验收标准

### 功能验收
1. **分析面板**
   - ✅ 能够正确显示和调整窗口大小
   - ✅ 文本域支持粘贴和编辑
   - ✅ 智能分析按钮状态正确切换
   - ✅ 分析结果正确显示事件卡片

2. **文本分析**
   - ✅ 正确解析常见时间表达（明天、下周、具体日期）
   - ✅ 提取事件描述和上下文
   - ✅ 生成可编辑的事件卡片
   - ✅ 处理无时间信息的文本

3. **便签窗口**
   - ✅ 无边框窗口正确显示
   - ✅ 置顶功能正常工作
   - ✅ 拖拽移动功能正常
   - ✅ 悬浮控件正确显示和交互
   - ✅ 颜色切换功能正常

4. **数据持久化**
   - ✅ 便签数据正确保存到本地
   - ✅ 应用重启后便签正确恢复
   - ✅ 便签状态（位置、大小、颜色）正确保持

5. **便签分享**
   - ✅ 内容正确复制到剪贴板
   - ✅ 格式化文本易于阅读

### 质量标准
1. **代码质量**: TypeScript严格模式，ESLint规范
2. **性能**: 应用启动时间 < 3秒，便签创建响应 < 1秒
3. **稳定性**: 无内存泄漏，长时间运行稳定
4. **用户体验**: 界面响应流畅，操作直观

### 测试要求
1. **单元测试**: 核心分析逻辑测试覆盖率 > 80%
2. **集成测试**: 主要用户流程端到端测试
3. **手动测试**: 在Windows 10/11环境下完整功能测试

## 风险评估

### 技术风险
1. **NLP准确性**: chrono-node对复杂中文的解析可能不准确
   - 缓解：提供用户编辑功能，允许手动调整
2. **性能问题**: 大量便签窗口可能影响系统性能
   - 缓解：实现便签窗口的懒加载和资源回收
3. **Electron打包**: 应用体积较大
   - 缓解：优化依赖，使用Electron Builder压缩

### 用户体验风险
1. **学习成本**: 用户需要理解如何使用分析功能
   - 缓解：提供清晰的UI提示和示例
2. **误识别**: 系统可能识别出不相关的"事件"
   - 缓解：提供"忽略"功能，用户可选择不创建便签

## 项目里程碑

### 第一阶段：核心功能原型 (2周)
- 搭建Electron + React项目结构
- 实现分析面板基础UI
- 集成chrono-node文本分析
- 实现基础便签窗口

### 第二阶段：功能完善 (1周)
- 完善便签交互功能
- 实现数据持久化
- 添加便签分享功能
- UI细节优化

### 第三阶段：测试和打包 (1周)
- 完整功能测试
- 性能优化
- 应用打包和安装程序
- 文档编写

## 成功标准
1. 用户能够成功粘贴聊天记录并获得有用的事件提取
2. 生成的便签能够有效提醒用户重要事件
3. 应用运行稳定，不影响系统性能
4. 用户反馈积极，认为应用解决了实际问题