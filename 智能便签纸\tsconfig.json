{"compilerOptions": {"target": "ES2016", "esModuleInterop": true, "lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["*"], "@main/*": ["main/*"], "@renderer/*": ["renderer/*"], "@types/*": ["types/*"], "@utils/*": ["utils/*"]}, "declaration": true, "outDir": "./dist"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "release"]}