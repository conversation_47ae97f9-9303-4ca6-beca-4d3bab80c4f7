
// @ts-check

import eslint from '@eslint/js';
import tseslint from 'typescript-eslint';
import reactRecommended from 'eslint-plugin-react/configs/recommended.js';
import reactHooks from 'eslint-plugin-react-hooks';

export default tseslint.config(
  eslint.configs.recommended,
  ...tseslint.configs.recommended,
  {
    ...reactRecommended,
    files: ['src/**/*.tsx', 'src/**/*.jsx'],
    languageOptions: {
        ...reactRecommended.languageOptions,
        parserOptions: {
            ecmaFeatures: { jsx: true },
        },
    },
    settings: {
        react: {
            version: 'detect'
        }
    }
  },
  {
    files: ['src/renderer/**/*.tsx', 'src/renderer/**/*.ts'],
    plugins: {
      'react-hooks': reactHooks,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
    },
  },
  {
    files: ['**/*.ts', '**/*.tsx'],
    rules: {
        '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^' }],
        '@typescript-eslint/no-explicit-any': 'warn',
        'react/react-in-jsx-scope': 'off',
    }
  },
  {
    files: ['src/renderer/**/*.js', 'src/renderer/**/*.jsx', 'src/renderer/**/*.ts', 'src/renderer/**/*.tsx'],
    languageOptions: {
        globals: {
            browser: true,
            node: false
        }
    }
  },
  {
    files: ['src/main/**/*.ts', 'src/preload/**/*.ts', '*.js'],
    languageOptions: {
        globals: {
            node: true,
            browser: false
        }
    }
  },
  {
    ignores: ['dist/', 'node_modules/', 'release/', 'webpack.config.js', 'jest.config.js', 'eslint.config.old.js']
  }
);
