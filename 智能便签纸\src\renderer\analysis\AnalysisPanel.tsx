/**
 * 分析面板主组件
 */

import React, { useState } from 'react';
import { ExtractedEvent } from '../../types/index';

const AnalysisPanel: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [events, setEvents] = useState<ExtractedEvent[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [monitoringStatus, setMonitoringStatus] = useState('');

  const handleAnalyze = async () => {
    if (!inputText.trim()) {
      return;
    }

    setIsAnalyzing(true);
    setError(null);

    try {
      const extractedEvents = await window.electronAPI.analyzeText(inputText);
      setEvents(extractedEvents);
    } catch (err) {
      setError('分析失败，请重试');
      console.error('分析失败:', err);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleClear = () => {
    setInputText('');
    setEvents([]);
    setError(null);
  };

  const handleStartMonitoring = async () => {
    try {
      setIsAnalyzing(true);
      setMonitoringStatus('正在启动微信监控...');
      const result = await (window as any).electronAPI.startWeChatMonitoring();
      setIsMonitoring(true);
      setMonitoringStatus('✅ 微信监控已启动，正在监控聊天窗口...');
    } catch (error) {
      console.error('启动监控失败:', error);
      setMonitoringStatus('❌ 启动监控失败: ' + error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleStopMonitoring = async () => {
    try {
      setIsAnalyzing(true);
      setMonitoringStatus('正在停止微信监控...');
      const result = await (window as any).electronAPI.stopWeChatMonitoring();
      setIsMonitoring(false);
      setMonitoringStatus('微信监控已停止');
    } catch (error) {
      console.error('停止监控失败:', error);
      setMonitoringStatus('停止监控失败: ' + error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleCreateNote = async (event: ExtractedEvent) => {
    try {
      const noteData = {
        content: event.title,
        eventTimestamp: event.timestamp?.toISOString() || new Date().toISOString(),
        position: { x: 100, y: 100 },
        size: { width: 220, height: 180 },
        color: 'yellow' as const,
        isPinned: true,
        isCompleted: false
      };

      await window.electronAPI.createNote(noteData);
      
      // 从列表中移除已创建的事件
      setEvents(prev => prev.filter(e => e !== event));
    } catch (err) {
      console.error('创建便签失败:', err);
    }
  };

  return (
    <div className="h-100 d-flex flex-column">
      {/* 微信监控控制区域 */}
      <div className="p-3 border-bottom bg-primary text-white">
        <div className="d-flex justify-content-between align-items-center mb-2">
          <h5 className="mb-0">🤖 智聊便签 - 微信监控</h5>
          <div>
            {!isMonitoring ? (
              <button
                className="btn btn-light btn-sm"
                onClick={handleStartMonitoring}
                disabled={isAnalyzing}
              >
                {isAnalyzing ? '启动中...' : '开始监控微信'}
              </button>
            ) : (
              <button
                className="btn btn-outline-light btn-sm"
                onClick={handleStopMonitoring}
                disabled={isAnalyzing}
              >
                停止监控
              </button>
            )}
          </div>
        </div>
        {monitoringStatus && (
          <div className="small">
            状态: {monitoringStatus}
          </div>
        )}
      </div>

      {/* 文本输入区域 */}
      <div className="flex-grow-1 p-3 border-bottom">
        <label htmlFor="inputText" className="form-label fw-bold">
          聊天记录分析 {isMonitoring && <span className="badge bg-success ms-2">自动监控中</span>}
        </label>
        <textarea
          id="inputText"
          className="form-control h-100"
          placeholder="请在此处粘贴聊天记录...&#10;&#10;例如：&#10;明天下午3点开会&#10;周五晚上聚餐&#10;下周一交报告"
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          style={{ resize: 'none', minHeight: '200px' }}
        />
      </div>

      {/* 控制按钮区域 */}
      <div className="p-3 border-bottom bg-light">
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <button
              className="btn btn-primary"
              onClick={handleAnalyze}
              disabled={isAnalyzing || !inputText.trim()}
            >
              {isAnalyzing ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" />
                  分析中...
                </>
              ) : (
                '智能分析'
              )}
            </button>
            <button
              className="btn btn-outline-secondary ms-2"
              onClick={handleClear}
              disabled={isAnalyzing}
            >
              清空内容
            </button>
          </div>
          <small className="text-muted">支持自然语言时间表达</small>
        </div>
      </div>

      {/* 分析结果区域 */}
      <div className="flex-grow-1 p-3 overflow-auto">
        {error && (
          <div className="alert alert-danger" role="alert">
            {error}
          </div>
        )}

        {events.length === 0 && !error && !isAnalyzing && (
          <div className="text-center text-muted py-4">
            <div className="fs-1 mb-3">💡</div>
            <p>粘贴聊天记录后点击"智能分析"开始识别事件</p>
          </div>
        )}

        {events.map((event, index) => (
          <div key={index} className="card mb-3">
            <div className="card-body">
              <div className="d-flex justify-content-between align-items-start mb-2">
                <h6 className="card-title mb-0">{event.title}</h6>
                <span className={`badge ${event.confidence > 0.7 ? 'bg-success' : event.confidence > 0.4 ? 'bg-warning' : 'bg-secondary'}`}>
                  {Math.round(event.confidence * 100)}%
                </span>
              </div>
              
              <p className="card-text text-muted small mb-2">
                {event.description}
              </p>
              
              {event.timestamp && (
                <p className="card-text">
                  <small className="text-muted">
                    时间: {event.timestamp.toLocaleString('zh-CN')}
                  </small>
                </p>
              )}
              
              <div className="d-flex gap-2">
                <button
                  className="btn btn-primary btn-sm"
                  onClick={() => handleCreateNote(event)}
                >
                  创建便签
                </button>
                <button
                  className="btn btn-outline-secondary btn-sm"
                  onClick={() => setEvents(prev => prev.filter(e => e !== event))}
                >
                  忽略
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AnalysisPanel;