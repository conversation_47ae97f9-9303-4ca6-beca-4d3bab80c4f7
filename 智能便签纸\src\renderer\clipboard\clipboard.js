/**
 * 剪贴板监控界面逻辑
 */

class ClipboardUI {
    constructor() {
        this.isMonitoring = false;
        this.initializeElements();
        this.bindEvents();
        this.updateStatus();
    }

    initializeElements() {
        this.toggleBtn = document.getElementById('toggleBtn');
        this.analyzeBtn = document.getElementById('analyzeBtn');
        this.clearLogBtn = document.getElementById('clearLogBtn');
        this.closeBtn = document.getElementById('closeBtn');
        this.statusIndicator = document.getElementById('statusIndicator');
        this.statusText = document.getElementById('statusText');
        this.clipboardContent = document.getElementById('clipboardContent');
        this.extractedEvents = document.getElementById('extractedEvents');
        this.logArea = document.getElementById('logArea');
    }

    bindEvents() {
        this.toggleBtn.addEventListener('click', () => this.toggleMonitoring());
        this.analyzeBtn.addEventListener('click', () => this.analyzeClipboard());
        this.clearLogBtn.addEventListener('click', () => this.clearLog());
        this.closeBtn.addEventListener('click', () => this.closeWindow());

        // 定期更新状态
        setInterval(() => this.updateStatus(), 1000);
    }

    async toggleMonitoring() {
        try {
            if (this.isMonitoring) {
                const result = await window.electronAPI.stopClipboardMonitoring();
                this.addLog('监控已停止: ' + result.message);
            } else {
                const result = await window.electronAPI.startClipboardMonitoring();
                this.addLog('监控已启动: ' + result.message);
            }
            await this.updateStatus();
        } catch (error) {
            this.addLog('操作失败: ' + error.message, 'error');
        }
    }

    async analyzeClipboard() {
        try {
            this.addLog('正在分析当前剪贴板内容...');
            const result = await window.electronAPI.analyzeClipboard();
            
            // 更新剪贴板内容显示
            this.updateClipboardContent(result.content);
            
            // 更新提取的事件
            this.updateExtractedEvents(result.events);
            
            this.addLog(`分析完成，提取到 ${result.events.length} 个事件`);
        } catch (error) {
            this.addLog('分析失败: ' + error.message, 'error');
        }
    }

    async updateStatus() {
        try {
            const status = await window.electronAPI.getClipboardStatus();
            this.isMonitoring = status.isMonitoring;
            
            if (this.isMonitoring) {
                this.statusIndicator.className = 'status-indicator status-active';
                this.statusText.textContent = '监控中';
                this.toggleBtn.innerHTML = '<i class="bi bi-stop-fill me-1"></i>停止监控';
                this.toggleBtn.className = 'btn btn-danger btn-custom';
            } else {
                this.statusIndicator.className = 'status-indicator status-inactive';
                this.statusText.textContent = '未启动';
                this.toggleBtn.innerHTML = '<i class="bi bi-play-fill me-1"></i>启动监控';
                this.toggleBtn.className = 'btn btn-primary btn-custom';
            }

            // 更新剪贴板内容显示
            if (status.lastContent) {
                this.updateClipboardContent(status.lastContent);
            }
        } catch (error) {
            console.error('更新状态失败:', error);
        }
    }

    updateClipboardContent(content) {
        if (content && content.trim()) {
            const truncated = content.length > 200 ? content.substring(0, 200) + '...' : content;
            this.clipboardContent.innerHTML = `<pre style="white-space: pre-wrap; margin: 0;">${this.escapeHtml(truncated)}</pre>`;
        } else {
            this.clipboardContent.innerHTML = '<em class="text-muted">暂无内容</em>';
        }
    }

    updateExtractedEvents(events) {
        if (events && events.length > 0) {
            const eventsHtml = events.map(event => `
                <div class="event-item">
                    <div class="fw-bold">${this.escapeHtml(event.title || event.description || '未知事件')}</div>
                    <div class="text-muted small">
                        <i class="bi bi-clock me-1"></i>
                        ${event.datetime ? new Date(event.datetime).toLocaleString('zh-CN') : '时间未知'}
                    </div>
                    ${event.description ? `<div class="mt-1">${this.escapeHtml(event.description)}</div>` : ''}
                </div>
            `).join('');
            this.extractedEvents.innerHTML = eventsHtml;
        } else {
            this.extractedEvents.innerHTML = '<em class="text-muted">暂无提取的事件</em>';
        }
    }

    addLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        const logEntry = document.createElement('div');
        
        let icon = '';
        let className = '';
        switch (type) {
            case 'error':
                icon = '❌';
                className = 'text-danger';
                break;
            case 'success':
                icon = '✅';
                className = 'text-success';
                break;
            case 'warning':
                icon = '⚠️';
                className = 'text-warning';
                break;
            default:
                icon = 'ℹ️';
                className = 'text-info';
        }
        
        logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> <span class="${className}">${icon} ${this.escapeHtml(message)}</span>`;
        this.logArea.appendChild(logEntry);
        this.logArea.scrollTop = this.logArea.scrollHeight;
    }

    clearLog() {
        this.logArea.innerHTML = '<div>日志已清空</div>';
    }

    closeWindow() {
        window.close();
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new ClipboardUI();
});