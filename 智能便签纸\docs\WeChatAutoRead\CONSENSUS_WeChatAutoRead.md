# 共识文档：微信聊天内容自动化读取（全局热键方案）

**任务名:** WeChatAutoRead
**版本:** 1.0
**日期:** 2025年8月30日

## 1. 明确的需求描述

实现一个全局快捷键（Global Hotkey）。当用户在任何应用程序（如微信、浏览器、记事本等）中选中文本后，按动此快捷键，系统将自动捕获选中的文本，并将其发送到“智聊便签”应用进行分析，如果内容包含时间事件，则自动创建提醒便签。

## 2. 技术实现方案

- **核心模块:** 采用 Electron 的 `globalShortcut` 模块在应用后台注册一个系统级的快捷键（初步定为 `Alt+Q`）。
- **文本捕获:** 快捷键触发后，将通过 Electron 的 `clipboard` 模块实现。具体流程如下：
    1.  临时保存用户当前剪贴板中的内容。
    2.  以编程方式模拟一次“复制”操作，将被选中的文本覆盖到剪贴板中。
    3.  读取剪贴板，获取目标文本。
    4.  立刻将之前保存的内容恢复到用户剪贴板，确保用户无感知。
- **逻辑集成:** 获取到的文本将通过现有的IPC通信管道，传递给主进程中的 `TextAnalyzer` 服务进行分析，复用已有的事件识别和便签创建逻辑。

## 3. 任务边界限制

### 包含范围 (In Scope)
- `globalShortcut` 的注册与注销管理。
- 实现稳定、无感的选中区域文本捕获逻辑。
- 将捕获的文本与现有分析引擎进行集成。

### 不包含范围 (Out of Scope)
- 对现有的文本分析算法或便签UI进行任何修改。
- 为快捷键提供用户自定义配置界面（可在未来版本中添加）。
- 除文本外的其他格式捕获（如图片、文件）。

## 4. 验收标准

1.  应用启动后，快捷键 `Alt+Q` 被成功注册为系统全局快捷键。
2.  在外部应用（如记事本、微信）中选中文本，按下 `Alt+Q`，应用能成功捕获该文本。
3.  若捕获的文本包含有效的时间事件（如“明天下午3点开会”），系统能成功创建对应的桌面便签。
4.  若捕获的文本不含时间事件，或用户没有选中任何文本，应用不应创建便签，且不应报错。
5.  整个操作过程不应改变用户原始剪贴板的内容。
6.  应用退出时，快捷键能被成功注销，不占用系统资源。
