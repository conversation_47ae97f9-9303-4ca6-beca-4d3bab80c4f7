# 阶段2: DESIGN (架构设计文档)

**任务名:** WeChatAutoRead (方案C: 智能剪贴板)
**版本:** 3.0
**日期:** 2025年8月30日

---

## 1. 整体架构图

```mermaid
graph TD
    subgraph "用户操作"
        A[用户复制文本 Ctrl+C] --> B[系统剪贴板];
    end

    subgraph "主进程 (Main Process)"
        B -- 内容变化 --> C{ClipboardMonitor};
        C -- 触发分析 --> D[TextAnalyzer];
        D -- 返回事件 --> C;
        C -- 事件有效 --> E[SuggestionManager];
        E -- 创建/显示 --> F[SuggestionWindow];
        F -- 用户点击创建 --> E;
        E -- 创建指令 --> G[DataManager];
        G -- 返回便签数据 --> H[WindowManager];
        H -- 创建窗口 --> I[StickyNote Window];
    end

    subgraph "渲染进程 (Renderer Process)"
        F -- 包含 --> J[Suggestion UI (React)];
        I -- 包含 --> K[便签UI (React)];
    end
```

## 2. 分层设计和核心组件

本次功能将严格遵循现有分层，核心逻辑在主进程，UI在渲染进程。

- **核心服务 (主进程):**
    - **`ClipboardMonitor.ts` (重构):** 不再是简单的监控，而是成为一个核心协调器。它负责监听剪贴板，调用 `TextAnalyzer`，并在获得有效事件后，通知 `SuggestionManager`。
    - **`SuggestionManager.ts` (新增):** 一个全新的服务，负责管理“建议窗口”的整个生命周期。包括创建、显示、传递数据、接收用户响应（创建或忽略），以及在超时后自动关闭窗口。

- **UI组件 (渲染进程):**
    - **`suggestion/` (新增):** 一个全新的渲染进程目录，包含 `suggestion.html` 和 `suggestion.tsx`，用于构建那个小巧的、可点击的建议提示框。

- **复用的现有组件:**
    - `TextAnalyzer.ts`: 无需改动，直接复用其文本分析能力。
    - `DataManager.ts`: 无需改动，用于在用户确认后创建便签数据。
    - `WindowManager.ts`: 无需改动，用于在数据创建后，生成最终的便签窗口。

## 3. 数据流向图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Clipboard as 系统剪贴板
    participant CM as ClipboardMonitor
    participant TA as TextAnalyzer
    participant SM as SuggestionManager
    participant SW as SuggestionWindow
    participant Core as 核心服务(Data/Window)

    User->>Clipboard: 按下 Ctrl+C
    Clipboard->>CM: 触发 onClipboardChanged
    CM->>TA: analyzeText(clipboardText)
    TA-->>CM: 返回 event[]
    alt 事件数组不为空
        CM->>SM: showSuggestion(event)
        SM->>SW: 创建并显示建议窗口
        SW->>User: 展示“是否创建便签？”
        User->>SW: 点击“创建”
        SW->>SM: 响应“create-note”
        SM->>Core: 执行创建便签流程
        Core-->>User: 显示新的便签窗口
    end
```

## 4. 接口契约定义 (IPC)

- **主进程 -> 建议窗口:**
    - `show-suggestion(event: ExtractedEvent)`: 指令建议窗口显示，并传递解析出的事件数据。
- **建议窗口 -> 主进程:**
    - `suggestion-response(response: { action: 'create' | 'ignore', event: ExtractedEvent })`: 用户点击按钮后，将操作（创建/忽略）和事件数据传回主进程。

## 5. 异常处理策略

- **剪贴板读取失败:** 静默处理，记录日志，不打扰用户。
- **文本分析无结果:** 正常流程，不触发任何UI。
- **建议窗口创建失败:** 记录错误日志，本次复制操作的流程静默失败。
- **用户无响应:** `SuggestionManager` 内设置一个定时器（如10秒），若用户未点击任何按钮，自动关闭建议窗口，流程结束。