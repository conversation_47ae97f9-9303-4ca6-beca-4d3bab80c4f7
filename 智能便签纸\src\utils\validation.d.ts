/**
 * 数据验证工具函数
 */
import { NoteData, AppSettings, ExtractedEvent } from '../types/index';
/**
 * 验证便签数据
 */
export declare function validateNoteData(data: any): data is NoteData;
/**
 * 验证应用设置
 */
export declare function validateAppSettings(data: any): data is AppSettings;
/**
 * 验证提取的事件
 */
export declare function validateExtractedEvent(data: any): data is ExtractedEvent;
/**
 * 清理和标准化便签数据
 */
export declare function sanitizeNoteData(data: Partial<NoteData>): Partial<NoteData>;
/**
 * 生成默认便签数据
 */
export declare function createDefaultNoteData(overrides?: Partial<NoteData>): Omit<NoteData, 'id' | 'createdAt' | 'updatedAt'>;
