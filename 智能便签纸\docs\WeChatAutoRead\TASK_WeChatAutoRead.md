# 阶段3: ATOMIZE (原子任务拆分)

**任务名:** WeChatAutoRead (方案C: 智能剪贴板)
**版本:** 3.0
**日期:** 2025年8月30日

---

## 1. 任务依赖图

```mermaid
graph TD
    T1[T1: 创建建议窗口UI] --> T3[T3: 实现建议管理器];
    T2[T2: 重构剪贴板监控] --> T3;
    T3 --> T4[T4: 集成到主进程];
    T4 --> T5[T5: 端到端手动测试];
```

## 2. 原子任务详细定义

### **T1: 创建建议窗口UI (`suggestion/`)**
- **优先级:** 高
- **输入契约:** React, antd (或其他UI库) 环境可用。
- **输出契约:**
    - 创建 `src/renderer/suggestion/` 目录，包含 `suggestion.html`, `suggestion.tsx`。
    - `suggestion.tsx` 实现一个React组件，能接收IPC消息传来的事件数据并显示，包含“创建”和“忽略”按钮。
    - 按钮点击后，能通过IPC将用户的选择发回主进程。
- **实现约束:** 窗口应小巧、无边框、可置于屏幕右下角。UI风格需与现有便签保持一致。
- **验收标准:** 该页面能在浏览器中独立渲染，并能模拟收发IPC消息。

---

### **T2: 重构剪贴板监控 (`ClipboardMonitor`)**
- **优先级:** 高
- **输入契约:** `TextAnalyzer` 服务可用。
- **输出契约:**
    - 重写 `src/main/ClipboardMonitor.ts`。
    - 实现 `startMonitoring` 和 `stopMonitoring` 方法。
    - 监听剪贴板 `text-changed` 事件，调用 `TextAnalyzer` 分析文本。
    - 分析结果有效时，调用 `SuggestionManager.showSuggestion(event)`。
- **实现约束:** 需处理好对同一文本的重复分析问题（例如，短时间内多次复制相同内容）。
- **验收标准:** 单元测试可验证：复制普通文本无反应；复制含时间事件的文本时，`SuggestionManager.showSuggestion` 被正确调用。

---

### **T3: 实现建议管理器 (`SuggestionManager`)**
- **优先级:** 高
- **依赖:** T1, T2
- **输入契约:** `DataManager`, `WindowManager` 服务可用。
- **输出契约:**
    - 创建 `src/main/SuggestionManager.ts` 文件。
    - 实现 `showSuggestion(event)` 方法，该方法负责创建并管理一个建议窗口（BrowserWindow）。
    - 监听来自建议窗口的IPC响应 `suggestion-response`。
    - 如果响应为 'create'，则调用 `DataManager` 和 `WindowManager` 创建便签。
    - 窗口显示后，启动一个10秒的自动销毁定时器。
- **实现约束:** 需管理好窗口的创建和销毁，确保任何时候最多只有一个建议窗口存在。
- **验收标准:** 单元测试可验证 `showSuggestion` 能创建窗口，且在收到不同IPC响应或超时后，能正确地执行创建或销毁逻辑。

---

### **T4: 集成到主进程 (`main.ts`)**
- **优先级:** 高
- **依赖:** T3
- **输入契约:** `main.ts` 文件存在。
- **输出契约:**
    - 在 `main.ts` 中实例化 `ClipboardMonitor` 和 `SuggestionManager`。
    - 在应用 `ready` 事件后，调用 `clipboardMonitor.startMonitoring()`。
- **实现约束:** 确保服务在应用生命周期的正确节点被初始化。
- **验收标准:** 应用启动后，剪贴板监控自动开始工作。

---

### **T5: 端到端手动测试**
- **优先级:** 高
- **依赖:** T4
- **输入契约:** 整个应用已编译并正在运行。
- **输出契约:** 一份测试报告，确认功能符合验收标准。
- **验收标准:**
    1. 复制普通文本，无任何反应。
    2. 复制包含“明天下午3点开会”的文本，屏幕右下角弹出建议窗口。
    3. 点击建议窗口的“创建”按钮，一个新的便签被成功创建。
    4. 再次复制含时间事件的文本，在建议窗口上点击“忽略”，便签未被创建，窗口消失。
    5. 建议窗口弹出后，不进行任何操作，10秒后窗口自动消失。