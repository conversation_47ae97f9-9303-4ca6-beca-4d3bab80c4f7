# 智聊便签项目对齐文档

## 项目概述
智聊便签是一个集成AI对话功能的智能便签应用，支持多模态输入和智能管理功能。

## 原始需求分析
基于项目规划报告，核心需求是：
**将即时通讯中的"未来事件"信息自动提取并以桌面便签形式提醒用户**

### 核心问题
- 在高频次即时通讯中，有价值的"未来事件"信息被淹没和遗忘
- 手动记录过程打断思路且容易遗漏
- 非正式口头约定难以转化为正式提醒

### 解决方案
- 用户粘贴聊天记录到分析面板
- 系统自动识别时间和事件信息
- 生成桌面便签进行持续提醒
- 非侵入式设计，后台静默服务

## 项目特性规范

### 核心功能模块
1. **分析面板 (Analysis Panel)**
   - 主窗口，600px * 400px
   - 多行文本域用于粘贴聊天记录
   - "智能分析"和"清空内容"按钮
   - 分析结果区域显示事件卡片

2. **智能文本分析引擎**
   - 基于chrono-node的日期时间解析
   - 识别"明天下午3点"、"周五晚上"等自然语言时间
   - 提取事件描述和上下文
   - 生成可编辑的事件卡片

3. **便签式提醒窗口 (Sticky Note Window)**
   - 无边框、置顶、可拖拽的桌面便签
   - 淡黄色背景，圆角设计
   - 显示事件标题和格式化时间
   - 悬浮控件：分享、关闭、置顶、调色板

4. **数据持久化系统**
   - 使用electron-store本地存储
   - JSON格式保存便签数据
   - 启动时恢复所有未完成便签

5. **便签分享功能**
   - 将便签内容复制到系统剪贴板
   - 格式化为易读文本供微信等工具使用

6. **全局快捷键模块（后续版本）**
   - Win+Shift+S快捷键
   - 自动捕获选中文本并分析

### 技术架构规范
- **应用框架**: Electron
- **前端框架**: React 18 + TypeScript
- **UI组件库**: Bootstrap (如规划报告所述)
- **状态管理**: React Context + useReducer
- **本地存储**: electron-store
- **NLP引擎**: chrono-node (日期解析)
- **AI服务**: OpenAI GPT-4 API
- **系统交互**: Electron APIs (globalShortcut, clipboard, etc.)
- **打包**: Electron Builder

### 边界确认
**包含范围：**
- Web应用开发（响应式设计）
- 基础AI对话功能
- 文本和图片输入支持
- 基础的便签管理功能
- 用户认证和数据同步

**不包含范围：**
- 移动端原生应用
- 高级AI功能（如训练自定义模型）
- 复杂的协作功能
- 高级数据分析功能

### 需求理解确认
1. **用户体验优先**: 界面简洁直观，操作流畅
2. **AI集成深度**: AI不仅是聊天工具，而是便签的智能助手
3. **数据安全**: 用户数据加密存储，隐私保护
4. **性能要求**: 快速响应，支持大量便签
5. **扩展性**: 架构支持后续功能扩展

### 疑问澄清
1. **AI对话范围**: ✅ 已确认 - 两种模式都支持，用户可以选择（单便签对话 vs 跨便签对话）
2. **语音功能**: 是否需要语音播放AI回复？
3. **协作功能**: 是否需要便签分享功能？
4. **数据导出**: 是否需要便签导出功能（PDF、Word等）？
5. **付费模式**: 是否考虑付费功能（如高级AI功能）？

### 已确认的设计决策
1. **应用类型**: ✅ 已确认 - Electron桌面应用
2. **开发策略**: ✅ 已确认 - 先实现基础版本，AI功能后续添加
3. **数据模型**: ✅ 已确认 - 完全按照规划报告的数据模型实现
4. **AI对话模式**: ✅ 已确认 - 支持两种模式（后续版本）
   - 单便签模式：只基于当前选中便签内容对话
   - 全局模式：基于所有便签内容进行跨便签对话
   - 用户可在界面中切换模式

### 数据模型规范（按规划报告）
```json
{
  "notes": [
    {
      "id": "uuid-string",
      "content": "事件描述",
      "eventTimestamp": "iso-timestamp",
      "position": { "x": 1150, "y": 240 },
      "size": { "width": 220, "height": 180 },
      "color": "yellow",
      "isPinned": true,
      "isCompleted": false
    }
  ],
  "settings": { "launchAtLogin": true }
}
```

### MVP版本功能范围（基础版本）
1. **分析面板**: 文本粘贴 + 基于chrono-node的日期时间解析
2. **便签管理**: 创建、编辑、删除、保存便签
3. **便签窗口**: 无边框置顶窗口，支持拖拽、调色、置顶控制
4. **数据持久化**: 本地JSON存储（electron-store）
5. **基础交互**: 便签分享到剪贴板

### 后续版本功能（V2.0+）
1. **AI对话功能**: OpenAI API集成
2. **全局快捷键**: 快速捕获选中文本
3. **高级NLP**: 更智能的事件识别
4. **多模态输入**: 图片、语音支持

### 技术约束
1. **API限制**: OpenAI API调用频率和成本控制
2. **存储限制**: Supabase免费版存储和带宽限制
3. **性能约束**: 前端包大小控制，加载速度优化
4. **浏览器兼容**: 支持现代浏览器（Chrome、Firefox、Safari、Edge）

### 验收标准
1. **功能完整性**: 所有核心功能正常工作
2. **用户体验**: 界面友好，操作直观
3. **性能指标**: 页面加载时间 < 3秒，API响应时间 < 2秒
4. **安全性**: 通过基础安全测试
5. **兼容性**: 在主流浏览器正常运行