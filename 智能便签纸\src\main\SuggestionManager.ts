
import { BrowserWindow, ipcMain, screen } from 'electron';
import * as path from 'path';
import { ExtractedEvent } from '../types';
import { DataManager } from './DataManager';
import { WindowManager } from './WindowManager';
import { logger } from './Logger';

export class SuggestionManager {
    private suggestionWindow: BrowserWindow | null = null;
    private dataManager: DataManager;
    private windowManager: WindowManager;
    private closeTimer: NodeJS.Timeout | null = null;

    constructor(dataManager: DataManager, windowManager: WindowManager) {
        this.dataManager = dataManager;
        this.windowManager = windowManager;
        this.handleIPC();
    }

    public showSuggestion(event: ExtractedEvent): void {
        logger.log('showSuggestion called with event:', event);
        try {
            if (this.suggestionWindow && !this.suggestionWindow.isDestroyed()) {
                logger.log('Closing existing suggestion window.');
                this.suggestionWindow.close();
            }

            const { width } = screen.getPrimaryDisplay().workAreaSize;
            const windowWidth = 350;
            const windowHeight = 150;
            const windowX = width - windowWidth - 20;
            const windowY = 60;
            
            logger.log(`Creating new suggestion window at (${windowX}, ${windowY}) with size ${windowWidth}x${windowHeight}`);

            this.suggestionWindow = new BrowserWindow({
                width: windowWidth,
                height: windowHeight,
                x: windowX,
                y: windowY,
                frame: false,
                alwaysOnTop: true,
                resizable: false,
                movable: true,
                skipTaskbar: true,
                webPreferences: {
                    preload: path.join(__dirname, '../preload/preload.js'),
                    nodeIntegration: false,
                    contextIsolation: true,
                },
            });

            const htmlPath = path.join(__dirname, '../renderer/suggestion/suggestion.html');
            logger.log('Loading suggestion HTML:', htmlPath);
            this.suggestionWindow.loadFile(htmlPath);

            this.suggestionWindow.once('ready-to-show', () => {
                logger.log('Suggestion window is ready to show. Sending event to renderer...');
                this.suggestionWindow?.webContents.send('show-suggestion', event);
                this.suggestionWindow?.show();
                
                this.closeTimer = setTimeout(() => {
                    logger.log('Suggestion window timed out. Closing.');
                    this.closeSuggestion();
                }, 10000);
            });

            this.suggestionWindow.on('closed', () => {
                logger.log('Suggestion window closed.');
                if (this.closeTimer) {
                    clearTimeout(this.closeTimer);
                    this.closeTimer = null;
                }
                this.suggestionWindow = null;
            });
        } catch (err) {
            logger.error('Error in showSuggestion:', err);
        }
    }

    private closeSuggestion(): void {
        if (this.suggestionWindow && !this.suggestionWindow.isDestroyed()) {
            this.suggestionWindow.close();
        }
    }

    private handleIPC(): void {
        ipcMain.on('suggestion-response', async (ipcEvent, response) => {
            logger.log('Received suggestion-response from renderer:', response);
            const { action, event } = response;
            if (action === 'create') {
                try {
                    logger.log('User confirmed note creation for event:', event.title);
                    const noteData = {
                        content: event.description || event.title,
                        eventTimestamp: (event.timestamp || new Date()).toISOString(),
                        position: { x: 100, y: 100 },
                        size: { width: 220, height: 180 },
                        color: 'yellow' as const,
                        isPinned: false,
                        isCompleted: false,
                    };
                    const noteId = await this.dataManager.createNote(noteData);
                    const fullNoteData = await this.dataManager.getNote(noteId);
                    if (fullNoteData) {
                        await this.windowManager.createNoteWindow(fullNoteData);
                    }
                } catch (err) {
                    logger.error('Error creating note from suggestion:', err);
                }
            }
            this.closeSuggestion();
        });
    }
}
