{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": false, "resolveJsonModule": true, "declaration": true, "sourceMap": true}, "include": ["src/main/**/*", "src/types/**/*", "src/utils/**/*"], "exclude": ["node_modules", "dist", "src/renderer/**/*", "src/preload/**/*"]}