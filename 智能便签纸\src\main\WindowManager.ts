/**
 * 窗口管理器 - 负责创建和管理所有应用窗口
 */

import { BrowserWindow, screen } from 'electron';
import * as path from 'path';
import { NoteData } from '../types/index';

export class WindowManager {
  private mainWindow: BrowserWindow | null = null;
  private analysisWindow: BrowserWindow | null = null;
  private clipboardWindow: BrowserWindow | null = null;
  private noteWindows: Map<string, BrowserWindow> = new Map();

  constructor() {
    // 初始化窗口管理器
  }

  /**
   * 创建主窗口
   */
  async createMainWindow(): Promise<BrowserWindow> {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.focus();
      return this.mainWindow;
    }

    this.mainWindow = new BrowserWindow({
      width: 800,
      height: 600,
      minWidth: 600,
      minHeight: 400,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../preload/preload.js')
      },
      title: '智聊便签',
      icon: path.join(__dirname, '../../assets/icon.png'),
      show: false
    });

    const htmlPath = path.join(__dirname, '../renderer/main/main.html');
    await this.mainWindow.loadFile(htmlPath);

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    return this.mainWindow;
  }

  /**
   * 获取主窗口
   */
  getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  /**
   * 创建分析面板窗口
   */
  async createAnalysisWindow(): Promise<BrowserWindow> {
    if (this.analysisWindow && !this.analysisWindow.isDestroyed()) {
      this.analysisWindow.focus();
      return this.analysisWindow;
    }

    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    
    // 计算窗口位置（居中显示）
    const windowWidth = 900;
    const windowHeight = 700;
    const x = Math.floor((width - windowWidth) / 2);
    const y = Math.floor((height - windowHeight) / 2);

    this.analysisWindow = new BrowserWindow({
      width: windowWidth,
      height: windowHeight,
      minWidth: 600,
      minHeight: 500,
      x,
      y,
      title: '智聊便签 - 分析面板',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../preload/preload.js')
      },
      icon: path.join(__dirname, '../../assets/icon.png'),
      show: false
    });

    const htmlPath = path.join(__dirname, '../renderer/analysis/index.html');
    await this.analysisWindow.loadFile(htmlPath);

    this.analysisWindow.once('ready-to-show', () => {
      this.analysisWindow?.show();
    });

    this.analysisWindow.on('closed', () => {
      this.analysisWindow = null;
    });

    return this.analysisWindow;
  }

  /**
   * 获取分析窗口
   */
  getAnalysisWindow(): BrowserWindow | null {
    return this.analysisWindow;
  }

  /**
   * 创建剪贴板监控窗口
   */
  async createClipboardWindow(): Promise<BrowserWindow> {
    if (this.clipboardWindow && !this.clipboardWindow.isDestroyed()) {
      this.clipboardWindow.focus();
      return this.clipboardWindow;
    }

    this.clipboardWindow = new BrowserWindow({
      width: 900,
      height: 700,
      minWidth: 600,
      minHeight: 500,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../preload/preload.js')
      },
      title: '剪贴板监控 - 智聊便签',
      icon: path.join(__dirname, '../../assets/icon.png'),
      show: false
    });

    const htmlPath = path.join(__dirname, '../renderer/clipboard/clipboard.html');
    await this.clipboardWindow.loadFile(htmlPath);

    this.clipboardWindow.once('ready-to-show', () => {
      this.clipboardWindow?.show();
    });

    this.clipboardWindow.on('closed', () => {
      this.clipboardWindow = null;
    });

    return this.clipboardWindow;
  }

  /**
   * 获取剪贴板监控窗口
   */
  getClipboardWindow(): BrowserWindow | null {
    return this.clipboardWindow;
  }

  /**
   * 创建便签窗口
   */
  async createNoteWindow(noteData: NoteData): Promise<BrowserWindow> {
    // 如果便签窗口已存在，则聚焦并返回
    if (this.noteWindows.has(noteData.id)) {
      const existingWindow = this.noteWindows.get(noteData.id);
      if (existingWindow && !existingWindow.isDestroyed()) {
        existingWindow.focus();
        return existingWindow;
      }
    }

    const noteWindow = new BrowserWindow({
      width: noteData.size.width,
      height: noteData.size.height,
      x: noteData.position.x,
      y: noteData.position.y,
      frame: false,
      alwaysOnTop: noteData.isPinned,
      resizable: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../preload/preload.js')
      },
      title: `便签 - ${noteData.content.substring(0, 20)}...`,
      show: false
    });

    const htmlPath = path.join(__dirname, '../renderer/note/index.html');
    await noteWindow.loadFile(htmlPath);

    // 窗口准备显示时发送便签数据
    noteWindow.once('ready-to-show', () => {
      noteWindow.webContents.send('note-data', noteData);
      noteWindow.show();
    });

    // 窗口关闭时清理
    noteWindow.on('closed', () => {
      this.noteWindows.delete(noteData.id);
    });

    this.noteWindows.set(noteData.id, noteWindow);
    return noteWindow;
  }

  /**
   * 获取便签窗口
   */
  getNoteWindow(noteId: string): BrowserWindow | null {
    return this.noteWindows.get(noteId) || null;
  }

  /**
   * 关闭便签窗口
   */
  closeNoteWindow(noteId: string): void {
    const noteWindow = this.noteWindows.get(noteId);
    if (noteWindow && !noteWindow.isDestroyed()) {
      noteWindow.close();
    }
    this.noteWindows.delete(noteId);
  }

  /**
   * 关闭窗口
   */
  closeWindow(windowId: string): void {
    // 根据窗口ID关闭对应窗口
    if (windowId === 'main' && this.mainWindow) {
      this.mainWindow.close();
    } else if (windowId === 'analysis' && this.analysisWindow) {
      this.analysisWindow.close();
    } else if (windowId === 'clipboard' && this.clipboardWindow) {
      this.clipboardWindow.close();
    } else {
      this.closeNoteWindow(windowId);
    }
  }

  /**
   * 最小化窗口
   */
  minimizeWindow(windowId: string): void {
    let window: BrowserWindow | null = null;
    
    if (windowId === 'main') {
      window = this.mainWindow;
    } else if (windowId === 'analysis') {
      window = this.analysisWindow;
    } else if (windowId === 'clipboard') {
      window = this.clipboardWindow;
    } else {
      window = this.noteWindows.get(windowId) || null;
    }

    if (window && !window.isDestroyed()) {
      window.minimize();
    }
  }

  /**
   * 切换置顶状态
   */
  toggleAlwaysOnTop(windowId: string): boolean {
    let window: BrowserWindow | null = null;
    
    if (windowId === 'main') {
      window = this.mainWindow;
    } else if (windowId === 'analysis') {
      window = this.analysisWindow;
    } else if (windowId === 'clipboard') {
      window = this.clipboardWindow;
    } else {
      window = this.noteWindows.get(windowId) || null;
    }

    if (window && !window.isDestroyed()) {
      const isOnTop = window.isAlwaysOnTop();
      window.setAlwaysOnTop(!isOnTop);
      return !isOnTop;
    }
    
    return false;
  }

  /**
   * 恢复便签窗口
   */
  async restoreNoteWindows(): Promise<void> {
    // TODO: 从数据管理器获取所有便签并恢复窗口
    console.log('恢复便签窗口功能待实现');
  }

  /**
   * 保存所有窗口状态
   */
  async saveAllWindowStates(): Promise<void> {
    // TODO: 保存所有窗口的位置和状态
    console.log('保存窗口状态功能待实现');
  }
}