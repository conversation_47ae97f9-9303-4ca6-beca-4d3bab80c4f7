
import React, { useState, useEffect } from 'react';
import { createRoot, Root } from 'react-dom/client';
import { ExtractedEvent } from '../../types';

const SuggestionPanel = () => {
    const [event, setEvent] = useState<ExtractedEvent | null>(null);

    useEffect(() => {
        // @ts-ignore
        window.electronAPI.onShowSuggestion((eventData: ExtractedEvent) => {
            setEvent(eventData);
        });
    }, []);

    const handleResponse = (action: 'create' | 'ignore') => {
        // @ts-ignore
        window.electronAPI.sendSuggestionResponse({ action, event });
    };

    if (!event) {
        return <div className="container-fluid"><p>等待事件...</p></div>;
    }

    return (
        <div className="container-fluid">
            <p className="mb-2"><strong>检测到提醒事件：</strong></p>
            <p className="mb-3">{event.description || event.title}</p>
            <div className="d-flex justify-content-end">
                <button className="btn btn-sm btn-secondary me-2" onClick={() => handleResponse('ignore')}>忽略</button>
                <button className="btn btn-sm btn-primary" onClick={() => handleResponse('create')}>创建</button>
            </div>
        </div>
    );
};

const container = document.getElementById('root');
if (container) {
    const root: Root = createRoot(container);
    root.render(<SuggestionPanel />);
} else {
    console.error('Root element not found');
}
