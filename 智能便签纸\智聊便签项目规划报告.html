
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>“智聊便签 (Smart Chat Notes)” 项目最终规划报告</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }
        h1 { font-size: 2em; }
        h2 { font-size: 1.75em; }
        h3 { font-size: 1.5em; }
        h4 { font-size: 1.25em; }
        strong {
            color: #2980b9;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 10px;
        }
        pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        code {
            font-family: "Courier New", Courier, monospace;
        }
        .meta {
            font-size: 0.9em;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <h1>“智聊便签 (Smart Chat Notes)” 桌面应用项目最终规划报告</h1>
    <p class="meta"><strong>版本:</strong> 1.1</p>
    <p class="meta"><strong>日期:</strong> 2025年8月30日</p>

    <h2>1. 项目名称</h2>
    <p><strong>智聊便签 (Smart Chat Notes)</strong></p>

    <h2>2. 项目愿景</h2>
    <p>打造一款无缝集成于桌面工作流的智能助手，将用户从“阅读-理解-手动记录”的繁琐流程中解放出来，通过“环境化”和“非侵入式”的设计，确保任何在即时通讯中产生的想法或约定都不会被遗忘。</p>

    <h2>3. 核心问题与用户场景</h2>
    <h4>核心问题:</h4>
    <p>在高频次的即时通讯中，有价值的“未来事件”信息被淹没和遗忘。</p>
    <h4>用户场景 1: “社交达人”</h4>
    <ul>
        <li><strong>痛点:</strong> 在多个微信群里聊天，朋友A约下周五晚上聚餐，同事B说明天下午三点前给文件。信息分散，切换应用手动记事的过程打断思路，且容易遗漏。</li>
        <li><strong>期望:</strong> 有个工具能“听着”聊天，把这些事自动捞出来贴在桌面上。</li>
    </ul>
    <h4>用户场景 2: “远程工作者”</h4>
    <ul>
        <li><strong>痛点:</strong> 客户通过微信/Teams沟通需求，提到“下周三上午10点看效果”或“月底前交付最终版”。这些是重要的截止日期，但没有被正式地记录在日历中。</li>
        <li><strong>期望:</strong> 能快速将非正式的口头约定转化为强提醒的视觉标记。</li>
    </ul>

    <h2>4. 解决方案与设计哲学</h2>
    <p>开发一款独立的Windows桌面应用，其核心设计哲学是 <strong>“环境化” (Ambient)</strong> 和 <strong>“非侵入式” (Non-intrusive)</strong>。应用的大部分功能应在后台静默服务，只在需要时以最轻量的方式（桌面便签）呈现信息，最大程度减少对用户当前工作的打扰。</p>

    <h2>5. 模块化功能详细设计</h2>
    
    <h4>5.1. 分析面板 (Analysis Panel)</h4>
    <ul>
        <li><strong>5.1.1. 窗口规格:</strong> 类型为应用主窗口，有标准的标题栏和最小化/关闭按钮。初始尺寸600px * 400px，允许用户调整大小。</li>
        <li><strong>5.1.2. UI 布局:</strong> 顶部为多行文本域，中部为“智能分析”和“清空内容”按钮，底部为分析结果区域。</li>
        <li><strong>5.1.3. 详细交互流程:</strong> 用户粘贴文本后，“分析”按钮激活。点击后显示加载动画，分析完成后在结果区域显示“事件卡片”。</li>
    </ul>

    <h4>5.2. 智能文本分析引擎</h4>
    <ul>
        <li><strong>5.2.1. 核心技术:</strong> 初期选用成熟的JavaScript自然语言日期解析库，如 <strong>`chrono-node`</strong>。</li>
        <li><strong>5.2.2. 实体识别范围:</strong> 识别时间实体（相对、模糊、精确）和事件实体（通过关键词和上下文）。</li>
        <li><strong>5.2.3. 结果呈现（事件卡片）:</strong> 每个潜在事件生成一个卡片，包含可编辑的事件标题和时间，并提供“创建便签”和“忽略”按钮。</li>
    </ul>

    <h4>5.3. 便签式提醒窗口 (Sticky Note Window)</h4>
    <ul>
        <li><strong>5.3.1. 窗口核心属性:</strong> 无边框, 默认置顶, 可调整大小, 透明背景。</li>
        <li><strong>5.3.2. UI与交互设计:</strong> 默认淡黄色背景，圆角和阴影。悬浮时在四角出现“分享”、“关闭”、“置顶”和“调色板”图标。</li>
    </ul>

    <h4>5.4. 数据持久化方案</h4>
    <ul>
        <li><strong>5.4.1. 存储技术:</strong> 采用 <strong>`electron-store`</strong> 库，将数据以JSON格式保存在本地。</li>
        <li><strong>5.4.2. 数据模型 (Schema):</strong>
            <pre><code>
{
  "notes": [
    {
      "id": "uuid-string",
      "content": "事件描述",
      "eventTimestamp": "iso-timestamp",
      "position": { "x": 1150, "y": 240 },
      "size": { "width": 220, "height": 180 },
      "color": "yellow",
      "isPinned": true,
      "isCompleted": false
    }
  ],
  "settings": { "launchAtLogin": true }
}
            </code></pre>
        </li>
        <li><strong>5.4.3. 启动流程:</strong> 应用启动时，读取JSON，重新创建所有未完成的便签窗口。</li>
    </ul>

    <h4>5.5. (规划中) 全局快捷键模块</h4>
    <ul>
        <li><strong>5.5.1. 技术实现:</strong> 使用Electron的 <strong>`globalShortcut`</strong> 模块。</li>
        <li><strong>5.5.2. 详细流程:</strong> 注册系统级快捷键（如 `Win+Shift+S`），按下后捕获选中区的文本，并自动送入分析面板进行分析。</li>
    </ul>

    <h4>5.6. 便签分享功能</h4>
    <ul>
        <li><strong>5.6.1. 功能目标:</strong> 方便地将便签内容通过微信等聊天工具发送给他人。</li>
        <li><strong>5.6.2. 技术实现:</strong> 调用Electron的 `clipboard` API。</li>
        <li><strong>5.6.3. 详细交互流程:</strong> 点击便签上的“分享”图标，将格式化好的文本复制到剪贴板，并弹出“已复制”的提示。</li>
    </ul>

    <h2>6. 技术选型方案</h2>
    <ul>
        <li><strong>应用框架: Electron</strong> - 实现原生桌面功能的最佳选择。</li>
        <li><strong>用户界面 (UI): React (TypeScript) + Bootstrap</strong> - 保证开发效率、代码质量和应用的长期可维护性。</li>
        <li><strong>核心逻辑与NLP: Node.js + chrono-node</strong> - 提供强大的系统交互能力和日期识别功能。</li>
    </ul>

    <h2>7. 开发阶段建议</h2>
    <ol>
        <li><strong>第一阶段：核心功能原型 (MVP)</strong> - 搭建框架，实现分析面板和便签窗口的核心联动及本地存储。</li>
        <li><strong>第二阶段：体验优化与增强</strong> - 实现分享功能、便签自定义选项，并打磨UI细节。</li>
        <li><strong>第三阶段：高级功能</strong> - 实现全局快捷键功能和开机自启等设置。</li>
    </ol>

    <h2>8. 风险与挑战</h2>
    <ul>
        <li><strong>NLP准确性:</strong> 复杂语境可能出错。缓解策略：提供完整的编辑功能。</li>
        <li><strong>性能开销:</strong> 大量便签窗口可能占用内存。缓解策略：性能分析和资源休眠。</li>
        - <strong>快捷键冲突:</strong> 全局快捷键可能被占用。缓解策略：允许用户自定义快捷键。</li>
    </ul>

    <h2>9. 总结</h2>
    <p>本方案详细阐述了“智聊便签”项目从愿景到具体实现的全过程，融合了用户对UI的期望和对核心功能的需求。方案在技术上可行，在产品设计上逻辑闭环，可以作为项目启动和执行的坚实基础。</p>

</body>
</html>
