/**
 * Preload脚本 - 在渲染进程中提供安全的API接口
 */

import { contextBridge, ipcRenderer } from 'electron';
import { NoteData, AppSettings, ExtractedEvent } from '../types/index';

// 定义暴露给渲染进程的API
const electronAPI = {
  // 便签相关操作
  createNote: (noteData: Omit<NoteData, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> =>
    ipcRenderer.invoke('create-note', noteData),
  
  updateNote: (noteId: string, updates: Partial<NoteData>): Promise<void> =>
    ipcRenderer.invoke('update-note', noteId, updates),
  
  deleteNote: (noteId: string): Promise<void> =>
    ipcRenderer.invoke('delete-note', noteId),
  
  completeNote: (noteId: string): Promise<void> =>
    ipcRenderer.invoke('complete-note', noteId),
  
  getAllNotes: (): Promise<NoteData[]> =>
    ipcRenderer.invoke('get-all-notes'),

  // 设置相关操作
  getSettings: (): Promise<AppSettings> =>
    ipcRenderer.invoke('get-settings'),
  
  updateSettings: (updates: Partial<AppSettings>): Promise<void> =>
    ipcRenderer.invoke('update-settings', updates),

  // 文本分析
  analyzeText: (text: string): Promise<ExtractedEvent[]> =>
    ipcRenderer.invoke('analyze-text', text),

  // 窗口操作
  openAnalysisWindow: () => ipcRenderer.invoke('open-analysis-window'),
  openClipboardWindow: () => ipcRenderer.invoke('open-clipboard-window'),
  closeWindow: (windowId: string): Promise<void> =>
    ipcRenderer.invoke('close-window', windowId),
  
  minimizeWindow: (windowId: string): Promise<void> =>
    ipcRenderer.invoke('minimize-window', windowId),
  
  toggleAlwaysOnTop: (windowId: string): Promise<boolean> =>
    ipcRenderer.invoke('toggle-always-on-top', windowId),

  // 系统操作
  copyToClipboard: (text: string): Promise<void> =>
    ipcRenderer.invoke('copy-to-clipboard', text),

  // 微信监控
  startWeChatMonitoring: () => ipcRenderer.invoke('start-wechat-monitoring'),
  stopWeChatMonitoring: () => ipcRenderer.invoke('stop-wechat-monitoring'),
  getMonitoringStatus: () => ipcRenderer.invoke('get-monitoring-status'),

  // 剪贴板监控相关
  startClipboardMonitoring: () => ipcRenderer.invoke('start-clipboard-monitoring'),
  stopClipboardMonitoring: () => ipcRenderer.invoke('stop-clipboard-monitoring'),
  getClipboardStatus: () => ipcRenderer.invoke('get-clipboard-status'),
  analyzeClipboard: () => ipcRenderer.invoke('analyze-clipboard'),

  // 事件监听
  onNoteDataUpdated: (callback: (noteData: NoteData) => void) => {
    ipcRenderer.on('note-data-updated', (event, noteData) => callback(noteData));
  },

  onSettingsUpdated: (callback: (settings: AppSettings) => void) => {
    ipcRenderer.on('settings-updated', (event, settings) => callback(settings));
  },

  onNoteData: (callback: (noteData: NoteData) => void) => {
    ipcRenderer.on('note-data', (event, noteData) => callback(noteData));
  },

  // 移除事件监听
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  }
};

// 将API暴露给渲染进程
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// 类型声明（供TypeScript使用）
declare global {
  interface Window {
    electronAPI: typeof electronAPI;
  }
}