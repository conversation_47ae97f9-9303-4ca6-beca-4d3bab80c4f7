<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智聊便签</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
            cursor: move;
            user-select: none;
        }
        
        .note-container {
            width: 100%;
            height: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            position: relative;
            display: flex;
            flex-direction: column;
            padding: 15px;
        }
        
        .note-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .note-title {
            font-size: 14px;
            font-weight: 600;
            line-height: 1.4;
            margin-bottom: 10px;
            word-wrap: break-word;
        }
        
        .note-time {
            font-size: 12px;
            opacity: 0.8;
            margin-top: auto;
        }
        
        .control-panel {
            position: absolute;
            top: -30px;
            right: 0;
            display: none;
            background: rgba(0,0,0,0.8);
            border-radius: 4px;
            padding: 4px;
        }
        
        .note-container:hover .control-panel {
            display: flex;
        }
        
        .control-btn {
            width: 24px;
            height: 24px;
            border: none;
            background: transparent;
            color: white;
            cursor: pointer;
            border-radius: 2px;
            margin: 0 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        
        /* 颜色主题 */
        .note-yellow {
            background-color: #FFFFE0;
            border: 1px solid #F0E68C;
            color: #8B4513;
        }
        
        .note-blue {
            background-color: #E6F3FF;
            border: 1px solid #87CEEB;
            color: #2F4F4F;
        }
        
        .note-green {
            background-color: #F0FFF0;
            border: 1px solid #90EE90;
            color: #006400;
        }
        
        .note-pink {
            background-color: #FFE4E1;
            border: 1px solid #FFC0CB;
            color: #8B008B;
        }
        
        .note-orange {
            background-color: #FFF8DC;
            border: 1px solid #FFB347;
            color: #FF4500;
        }
        
        /* 调整大小手柄 */
        .resize-handle {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 12px;
            height: 12px;
            cursor: se-resize;
            background: linear-gradient(-45deg, transparent 0%, transparent 40%, currentColor 40%, currentColor 60%, transparent 60%);
            opacity: 0.3;
        }
        
        .resize-handle:hover {
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <div id="root" class="h-100">
        <div class="control-panel">
            <button class="control-btn" id="shareBtn" title="分享">📤</button>
            <button class="control-btn" id="pinBtn" title="置顶">📌</button>
            <button class="control-btn" id="colorBtn" title="换色">🎨</button>
            <button class="control-btn" id="closeBtn" title="关闭">✕</button>
        </div>
        
        <div class="note-content">
            <div class="note-title" id="noteTitle">加载中...</div>
            <div class="note-time" id="noteTime"></div>
        </div>
        
        <div class="resize-handle"></div>
    </div>
</body>
</html>