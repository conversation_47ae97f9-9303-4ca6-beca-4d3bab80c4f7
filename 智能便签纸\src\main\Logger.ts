
import * as fs from 'fs';
import * as path from 'path';

const logFilePath = path.join(__dirname, '../../debug.log');

// 清空旧的日志文件
fs.writeFileSync(logFilePath, '');

export const logger = {
    log: (message: string, ...args: any[]) => {
        const timestamp = new Date().toISOString();
        const formattedMessage = `[${timestamp}] [INFO] ${message} ${args.length > 0 ? JSON.stringify(args, null, 2) : ''}\n`;
        fs.appendFileSync(logFilePath, formattedMessage);
        console.log(message, ...args);
    },
    error: (message: string, ...args: any[]) => {
        const timestamp = new Date().toISOString();
        const formattedMessage = `[${timestamp}] [ERROR] ${message} ${args.length > 0 ? JSON.stringify(args, null, 2) : ''}\n`;
        fs.appendFileSync(logFilePath, formattedMessage);
        console.error(message, ...args);
    }
};
