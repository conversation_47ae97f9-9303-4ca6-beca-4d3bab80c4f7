### “智聊便签 (Smart Chat Notes)” 桌面应用项目最终规划报告

**版本: 1.1**
**日期: 2025年8月30日**

#### **1. 项目名称**

**智聊便签 (Smart Chat Notes)**

#### **2. 项目愿景**

打造一款无缝集成于桌面工作流的智能助手，将用户从“阅读-理解-手动记录”的繁琐流程中解放出来，通过“环境化”和“非侵入式”的设计，确保任何在即时通讯中产生的想法或约定都不会被遗忘。

#### **3. 核心问题与用户场景**

*   **核心问题:** 在高频次的即时通讯中，有价值的“未来事件”信息被淹没和遗忘。
*   **用户场景 1: “社交达人”**
    *   **痛点:** 在多个微信群里聊天，朋友A约下周五晚上聚餐，同事B说明天下午三点前给文件。信息分散，切换应用手动记事的过程打断思路，且容易遗漏。
    *   **期望:** 有个工具能“听着”聊天，把这些事自动捞出来贴在桌面上。
*   **用户场景 2: “远程工作者”**
    *   **痛点:** 客户通过微信/Teams沟通需求，提到“下周三上午10点看效果”或“月底前交付最终版”。这些是重要的截止日期，但没有被正式地记录在日历中。
    *   **期望:** 能快速将非正式的口头约定转化为强提醒的视觉标记。

#### **4. 解决方案与设计哲学**

开发一款独立的Windows桌面应用，其核心设计哲学是 **“环境化” (Ambient) 和 “非侵入式” (Non-intrusive)**。应用的大部分功能应在后台静默服务，只在需要时以最轻量的方式（桌面便签）呈现信息，最大程度减少对用户当前工作的打扰。

#### **5. 模块化功能详细设计**

**5.1. 分析面板 (Analysis Panel)**
*   **5.1.1. 窗口规格:**
    *   **类型:** 应用主窗口，有标准的标题栏和最小化/关闭按钮。
    *   **初始尺寸:** 600px * 400px (宽 * 高)，允许用户调整大小。
    *   **标题:** “智聊便签 - 分析面板”。
*   **5.1.2. UI 布局:**
    *   **顶部:** 一个可伸缩的多行文本域（`textarea`），占窗口约60%高度，带有“请在此处粘贴聊天记录...”的占位提示。
    *   **中部:** 一行操作按钮，包括一个主要的“智能分析”按钮和一个“清空内容”按钮。
    *   **底部:** “分析结果”区域，初始为空。
*   **5.1.3. 详细交互流程:**
    1.  用户启动应用，分析面板出现。
    2.  用户将外部文本粘贴入文本域，“智能分析”按钮从灰色变为可用状态。
    3.  用户点击“智能分析”，按钮显示加载动画（spinner），文本域变为只读。
    4.  分析完成后，加载动画消失，分析结果区域出现一个或多个“事件卡片”。
    5.  用户对事件卡片进行操作（创建便签/忽略）。
    6.  用户点击“清空内容”，文本域和分析结果区域被重置为初始状态。

**5.2. 智能文本分析引擎**
*   **5.2.1. 核心技术:**
    *   初期选用成熟的JavaScript自然语言日期解析库，如 **`chrono-node`**。它能优秀地处理如“明天下午3点”、“周五晚上”、“30号”等非结构化时间描述。
*   **5.2.2. 实体识别范围:**
    *   **时间实体:** 必须识别并转换为标准ISO 8601格式 (`YYYY-MM-DDTHH:mm:ss.sssZ`)。
    *   **事件实体:** 通过关键词触发和上下文关联来提取。
*   **5.2.3. 结果呈现（事件卡片）:**
    *   分析出的每个潜在事件，都在“分析结果”区域生成一个卡片。
    *   **卡片内容:**
        *   **事件标题 (可编辑):** 文本输入框，填充了引擎猜测的事件描述。
        *   **事件时间 (可编辑):** 日期和时间选择器，填充了引擎计算出的绝对时间。
        *   **操作按钮:** 一个醒目的 **“创建便签”** 按钮和一个“忽略”按钮。

**5.3. 便签式提醒窗口 (Sticky Note Window)**
*   **5.3.1. 窗口核心属性 (Electron BrowserWindow options):**
    *   `frame: false` (无边框), `alwaysOnTop: true` (默认置顶), `resizable: true`, `transparent: true`。
*   **5.3.2. UI与交互设计:**
    *   **默认外观:** `#FFFFE0` (淡黄色) 背景，圆角边框，轻微的CSS `box-shadow` 模拟立体感。
    *   **显示内容:** 顶部为事件标题，底部为格式化后的日期和时间。
    *   **悬浮控件 (鼠标悬停时出现):**
        *   **左上角:** “分享”图标。
        *   **右上角:** “X” (关闭)按钮。
        *   **右下角:** “图钉” (置顶/取消置顶)图标。
        *   **左下角:** “调色板” (切换颜色)图标。

**5.4. 数据持久化方案**
*   **5.4.1. 存储技术:**
    *   采用 **`electron-store`** 库，将数据以JSON格式保存在本地用户数据目录中。
*   **5.4.2. 数据模型 (Schema):**
    ```json
    {
      "notes": [
        {
          "id": "uuid-string",
          "content": "事件描述",
          "eventTimestamp": "iso-timestamp",
          "position": { "x": 1150, "y": 240 },
          "size": { "width": 220, "height": 180 },
          "color": "yellow",
          "isPinned": true,
          "isCompleted": false
        }
      ],
      "settings": { "launchAtLogin": true }
    }
    ```
*   **5.4.3. 启动流程:** 应用启动时，读取此JSON，重新在桌面上创建所有 `isCompleted: false` 的便签窗口，并恢复其位置、大小和颜色。

**5.5. (规划中) 全局快捷键模块**
*   **5.5.1. 技术实现:**
    *   使用Electron的 **`globalShortcut`** 模块在主进程中注册系统级的快捷键。
*   **5.5.2. 详细流程:**
    1.  在后台注册 `Win+Shift+S` (或其他可自定义的组合)。
    2.  按下快捷键后，通过 `robotjs` 等库捕获当前选中的文本。
    3.  将捕获的文本自动填入分析面板并触发分析。

**5.6. 便签分享功能**
*   **5.6.1. 功能目标:**
    *   允许用户将一个已创建的便签内容，方便地通过微信等聊天工具发送给他人。
*   **5.6.2. 技术实现:**
    *   调用Electron的 `clipboard` API，将程序格式化好的文本内容动态写入用户的系统剪贴板。
*   **5.6.3. 详细交互流程:**
    1.  用户点击便签左上角的“分享”图标。
    2.  应用将便签内容格式化为易读文本（例如：`【智聊便签 提醒】
- 事件: ...
- 时间: ...`）并复制到剪贴板。
    3.  屏幕弹出提示：“已复制到剪贴板，现在去微信粘贴发送吧！”。
    4.  用户切换到微信，手动粘贴发送。

#### **6. 技术选型方案**

*   **应用框架: Electron**
    *   **理由:** 实现自定义无边框窗口、全局快捷键、本地文件读写等原生桌面功能的最佳选择。
*   **用户界面 (UI): React (TypeScript) + Bootstrap**
    *   **理由:** 现代化的UI开发技术栈，保证开发效率、代码质量和应用的长期可维护性。
*   **核心逻辑与NLP: Node.js + chrono-node**
    *   **理由:** Node.js作为Electron的后端，提供强大的系统交互能力。`chrono-node`作为初期NLP引擎，能快速实现核心的日期识别功能。

#### **7. 开发阶段建议**

1.  **第一阶段：核心功能原型 (MVP)**
    *   **目标:** 搭建应用基础框架，实现“分析面板”和“便签窗口”的核心联动及本地存储。
    *   **产出:** 一个可以运行的基础版本：能粘贴文本、分析事件、生成/保存/加载便签。
2.  **第二阶段：体验优化与增强**
    *   **目标:** 提升应用的便利性和实用性。
    *   **产出:** 实现分享功能、便签自定义选项（颜色等）、并对UI细节进行打磨。
3.  **第三阶段：高级功能**
    *   **目标:** 进一步降低用户操作成本。
    *   **产出:** 实现并优化全局快捷键功能，在设置中增加如“开机自启”等选项。

#### **8. 风险与挑战**

*   **NLP准确性:** 对于复杂或有歧义的中文语境，NLP引擎可能出错。
    *   **缓解策略:** 在“事件卡片”中提供完整的编辑功能，让用户拥有最终解释权。
*   **性能开销:** 大量便签窗口可能会占用可观的系统内存。
    *   **缓解策略:** 在开发中对性能进行分析，对不活跃的便签窗口进行资源休眠。
*   **快捷键冲突:** 全局快捷键可能与其他应用冲突。
    *   **缓解策略:** 在设置中允许用户自定义快捷键。

#### **9. 总结**

本方案详细阐述了“智聊便签”项目从愿景到具体实现的全过程，融合了用户对UI的期望和对核心功能的需求。方案在技术上可行，在产品设计上逻辑闭环，可以作为项目启动和执行的坚实基础。
